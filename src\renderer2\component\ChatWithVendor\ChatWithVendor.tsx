import { useState, useEffect, useRef, useMemo } from 'react'
import styles from './ChatWithVendor.module.scss'
import { ReactComponent as EmojiIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Emoji.svg';
import { ReactComponent as EmojiIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Emoji-Hover.svg';
import { ReactComponent as BoldIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Bold.svg';
import { ReactComponent as BoldIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Bold-Hover.svg';
import { ReactComponent as ItalicIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Italic.svg';
import { ReactComponent as ItalicIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Italic-Hover.svg';
import { ReactComponent as UnderlineIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Underline.svg';
import { ReactComponent as UnderlineIconHover } from 'src/renderer2/assets/New-images/Share-Pricing/Underline-Hover.svg';
import { ReactComponent as AttachmentIcon } from 'src/renderer2/assets/New-images/attachment.svg';
import { ReactComponent as AttachmentHoverIcon } from 'src/renderer2/assets/New-images/attachment-hover.svg';
import { ReactComponent as ExpandIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Expand.svg';
import { ReactComponent as CloseIcon } from 'src/renderer2/assets/New-images/Share-Pricing/Close.svg';
import { ReactComponent as DownArrowIcon } from 'src/renderer2/assets/New-images/DownArrow.svg';
import usePostSendMessage from 'src/renderer2/hooks/usePostSendMessage';
import leoProfanity from 'leo-profanity';
import { v4 as uuidv4 } from 'uuid';
import { useDropzone } from 'react-dropzone';


import clsx from 'clsx';
import { commomKeys, uploadFileAndGetS3Url, useChatWithVendorStore, useGlobalStore, useImgixOrImageKit, userRole } from '@bryzos/giss-ui-library';
import usePostStartChat from 'src/renderer2/hooks/usePostStartChat';
import useDialogStore from '../DialogPopup/DialogStore';
import MessageBubble from './MessageBubble/MessageBubble';
import AttachmentBubble from './AttachmentBubble/AttachmentBubble';
import { cleanContentEditableHtml, formatDate, isScrollAtBottom, normalizeMessage, scrollToBottom, shortenFileName } from './chatutils';
import { Chip, Tooltip } from '@mui/material';

const ATTACHMENT = 'attachment';
const USE_IMGIX = 'USE_IMGIX';

const ChatWithVendor = ({close, userName}: any) => {
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [activeButtons, setActiveButtons] = useState({
        bold: false,
        italic: false,
        underline: false
    });
    const [lastClickedButton, setLastClickedButton] = useState<string | null>(null);
    const [message, setMessage] = useState('')
    const { channelName, poNumber, messages, setMessages, companyName, newMessages, setNewMessages } = useChatWithVendorStore();
    const [wasAtBottom, setWasAtBottom] = useState(true);
    const messageContainerRef = useRef<HTMLDivElement>(null);
    const [pendingFiles, setPendingFiles] = useState<File[]>([]);
    const {mutateAsync: startChat} = usePostStartChat();
    const {showCommonDialog, resetDialogStore} = useDialogStore();
    const [isUploadingFiles, setIsUploadingFiles] = useState(false);
    const [showNewMessageIndicator, setShowNewMessageIndicator] = useState(false);
    const [newMessageCount, setNewMessageCount] = useState(0);
    const [isImgix, setIsImgix] = useState(false);
    const imgixOrImageKit = useImgixOrImageKit();
    const userData = useGlobalStore((state: any) => state.userData);
    const isSeller = userData?.data?.type === userRole.sellerUser;

    const sellerPoNumber = useMemo(()=>{
        if(!channelName) return '';
        return 'P' + channelName.substring(1, channelName.length);
    },[channelName]);
    
    
    
    // Handle scroll events to track if we're at the bottom
    const handleScroll = () => {
        const isBottom = isScrollAtBottom(messageContainerRef.current, 30);
        setWasAtBottom(isBottom);
        
        // Hide indicator when scrolled to bottom
        if (isBottom && showNewMessageIndicator) {
            setShowNewMessageIndicator(false);
            setNewMessageCount(0);
        }
    };

    
    // When messages change, scroll to bottom if we were at bottom before
    useEffect(() => {
        if (wasAtBottom) {
            setTimeout(() => {
                scrollToBottom(messageContainerRef.current);
            }, 100);
        }
    }, [messages]);





    useEffect(()=>{
        console.log('newMessages',newMessages);
        if(newMessages && newMessages.length > 0){
            const newMessagesForThisChat = newMessages.filter(item=>item.reference_id === channelName);
            if(newMessagesForThisChat.length === 0) return;
            const mappedMessages = newMessagesForThisChat.map(item=>{
                const message = normalizeMessage(item, useGlobalStore.getState().userData.data.id.toString());
                return message;
            });

            // Check if user is scrolled up before adding new messages
            const wasScrolledUp = !wasAtBottom;
            
            mappedMessages.forEach(item => {
                if (item.isMyMessage) {
                    const index = messages.findIndex(mesg => mesg.id === item.id);
                    if (index >= 0) {
                        messages[index].status = 'sent';
                        messages[index].formattedTimestamp = item.formattedTimestamp;
                    } else {
                        messages.push(item);
                    }
                } else {
                    messages.push(item);
                    // Increment new message count if user is scrolled up
                    if (wasScrolledUp) {
                        setNewMessageCount(prev => prev + 1);
                        setShowNewMessageIndicator(true);
                    }
                }
            })

            setMessages([...messages]);
            
            const leftOverMessages = newMessages.filter(item=>item.reference_id !== channelName);
            setNewMessages(leftOverMessages);
        }
    },[newMessages]);

    useEffect(()=>{
        if(poNumber){
            getPastMessages();
            checkImgix();

        }
    },[poNumber]);

    
    const checkImgix = async () => {
        const response =  (await imgixOrImageKit.mutateAsync())?.data?.data;
        const imgixObj = response.find(res => res.config_key === USE_IMGIX);
        console.log('imgixObj', imgixObj);
        if (imgixObj) {
            setIsImgix(imgixObj.config_value);
        }
    }


    //export const uploadFileAndGetS3Url = async(file: File, bucketName: string, folderPath: string = '/', getSignedUrl: string, prefixUrl: string, environmentName: string): Promise<string> => {
    const sendPendingFiles = async () => {
        if (pendingFiles.length === 0) return;

        setIsUploadingFiles(true);
        const newMessageObjects = [];
        
        for (const file of pendingFiles) {
            const fileName = file.name.substring(0, file.name.lastIndexOf(".")) || file.name;
            const extension = file.name.split('.').pop();
            const s3Url = await uploadFileAndGetS3Url(
                file, //file
                import.meta.env.VITE_S3_UPLOAD_CHAT_BUCKET_NAME, //bucketName
                `/${poNumber}/`,//folderPath (environment comes by default)
                import.meta.env.VITE_API_SERVICE + '/user/get_signed_url',//SignedUrl 
                'chat', //Name prefix
                import.meta.env.VITE_ENVIRONMENT);
            const id = uuidv4();
            const timeStamp = formatDate(new Date());
            const messageObject = {
                id: id, 
                type: ATTACHMENT,
                name: fileName,
                extension: extension,
                url: s3Url,
                formattedTimestamp: timeStamp,
                status: 'sending',
                isMyMessage: true
            }
    
            newMessageObjects.push(messageObject);
            
            // Send the attachment message
            const sendMessageData = await sendMessage({
                id: id, 
                reference_id: channelName, 
                message: JSON.stringify(messageObject)
            });

            console.log(s3Url, 's3Url');
        }

        setMessages([...messages, ...newMessageObjects]);
        
        // Clear pending files after sending
        setPendingFiles([]);
        setIsUploadingFiles(false);
    };
    
    const {
        getRootProps,
        getInputProps,
        isDragActive,
        open: openFileDialog,
    } = useDropzone({
        maxFiles: 1,
        multiple: false,
        onDropAccepted: (files: File[]) => {
            setPendingFiles(files);
        },
        noClick: true, // Prevent default click on dropzone
        noKeyboard: true // Prevent keyboard opening file dialog
    });

    const visibleFiles = pendingFiles.slice(0, 2);
    const remainingFiles = pendingFiles.slice(2);
const remainingCount = pendingFiles.length - visibleFiles.length;
    

    const getPastMessages = async ()=>{
        const myId = useGlobalStore.getState().userData.data.id.toString();
        const channelData = await startChat({reference_id: channelName});
        if(channelData?.error_message){
            console.log("error_message ", channelData.error_message);
            showCommonDialog(null, channelData.error_message , null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            return;
        }
        let messages = [];
        
        if(channelData?.chats&&channelData?.chats?.length > 0){
            messages = channelData.chats.map(item=>{
                const message = normalizeMessage(item, myId);
                return message;
            });
            messages = messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        }
        setMessages(messages);
    }

    // Common emojis for the picker
    const commonEmojis = [
        "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
        "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
        "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
        "👍", "👎", "👌", "✌️", "🤞", "🤝", "🙏", "👏", "🎉", "❤️"
    ];
    const {mutateAsync: sendMessage, isLoading} = usePostSendMessage(); 

    const isSendingMessage = isUploadingFiles || isLoading;
    

    const handleSendMessage = async () => {
        console.log('messageInputRef', messageInputRef.current);
        
        const hasText = messageInputRef.current && messageInputRef.current.innerHTML.trim() && 
            messageInputRef.current.textContent.trim();
        const hasFiles = pendingFiles.length > 0;
        
        // If we have both text and files, send them together
        if (hasText && hasFiles) {
            await sendTextWithFiles();
        } else if (hasFiles) {
            // Send only files
            await sendPendingFiles();
        } else if (hasText) {
            // Send only text
            await sendTextOnly();
        }
        setTimeout(() => {
            messageInputRef.current?.focus();
        }, 200);
    }
    
    const sendTextWithFiles = async () => {
        setIsUploadingFiles(true);
        const rawMessage = cleanContentEditableHtml(messageInputRef.current.innerHTML);
        const messageContent = leoProfanity.clean(rawMessage);
        const timeStamp = formatDate(new Date());
        const id = uuidv4();
        
        // Create attachment objects for files
        const attachmentObjects = [];
        
        for (const file of pendingFiles) {
            const fileName = file.name.substring(0, file.name.lastIndexOf(".")) || file.name;
            const extension = file.name.split('.').pop();
            const s3Url = await uploadFileAndGetS3Url(
                file,
                import.meta.env.VITE_S3_UPLOAD_CHAT_BUCKET_NAME,
                `/${poNumber}/`,
                import.meta.env.VITE_API_SERVICE + '/user/get_signed_url',
                'chat',
                import.meta.env.VITE_ENVIRONMENT
            );
            
            attachmentObjects.push({
                name: fileName,
                extension: extension,
                url: s3Url
            });
        }
        
        // Create combined message object
        const combinedMessageObject = {
            id: id,
            text: messageContent,
            attachments: attachmentObjects,
            formattedTimestamp: timeStamp,
            status: 'sending',
            isMyMessage: true,
            isCombined: true
        };
        
        // Add to messages immediately
        setMessages([...messages, combinedMessageObject]);
        
        // Clear input and pending files
        messageInputRef.current.innerHTML = '';
        setPendingFiles([]);
        
        // Send the combined message
        const sendMessageData = await sendMessage({
            id: id,
            reference_id: channelName,
            message: JSON.stringify(combinedMessageObject)
        });
        setIsUploadingFiles(false);
    };
    
    const sendTextOnly = async () => {
        const rawMessage = cleanContentEditableHtml(messageInputRef.current.innerHTML);
        const messageContent = leoProfanity.clean(rawMessage);
        const timeStamp = formatDate(new Date());
        const id = uuidv4();
        
        setMessages([...messages, { 
            id: id, 
            text: messageContent, 
            formattedTimestamp: timeStamp, 
            status: 'sending', 
            isMyMessage: true 
        }]);
        
        // Clear the input after sending
        messageInputRef.current.innerHTML = '';
        
        const sendMessageData = await sendMessage({
            id: id, 
            reference_id: channelName, 
            message: JSON.stringify({
                id: id, 
                text: messageContent, 
                formattedTimestamp: timeStamp 
            })
        });
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    }

    // Close emoji picker when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (showEmojiPicker && !target.closest(`.${styles.emojiPicker}`) && !target.closest(`.${styles.emojiButton}`)) {
                setShowEmojiPicker(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showEmojiPicker]);
    const handleEmojiButtonClick = () => {
        setShowEmojiPicker(!showEmojiPicker);
        setLastClickedButton('emoji');
    };

    const insertEmoji = (emoji: string) => {
        if (messageInputRef.current) {
            messageInputRef.current.focus();
            document.execCommand('insertText', false, emoji);
        }
        setShowEmojiPicker(false);
    }
    
    // function formatDate(date) {
    //     const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
    //                     "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    //     const month = months[date.getMonth()];
    //     const day = date.getDate();
    //     const year = date.getFullYear();
        
    //     let hours = date.getHours();
    //     const minutes = String(date.getMinutes()).padStart(2, "0");
    //     const ampm = hours >= 12 ? "pm" : "am";
    //     hours = hours % 12 || 12;
        
    //     return `${month} ${day}, ${year} ${hours}:${minutes}${ampm}`;
    // }

    // Add a new ref for the contentEditable div
    const messageInputRef = useRef<HTMLDivElement>(null);

    // Update handleFormat function to use document.execCommand
    const handleFormat = (command: string) => {
        document.execCommand(command, false);
        if (messageInputRef.current) {
            messageInputRef.current.focus();
            
            // Update active states
            setActiveButtons({
                ...activeButtons,
                [command]: document.queryCommandState(command)
            });
            
            setLastClickedButton(command);
        }
    };

    // Add a function to check active styles when selection changes
    useEffect(() => {
        const checkActiveStyles = () => {
            if (document.queryCommandState) {
                setActiveButtons({
                    bold: document.queryCommandState('bold'),
                    italic: document.queryCommandState('italic'),
                    underline: document.queryCommandState('underline')
                });
            }
        };

        // Add event listeners for selection change
        document.addEventListener('selectionchange', checkActiveStyles);
        if (messageInputRef.current) {
            messageInputRef.current.addEventListener('click', checkActiveStyles);
            messageInputRef.current.addEventListener('keyup', checkActiveStyles);
        }

        return () => {
            document.removeEventListener('selectionchange', checkActiveStyles);
            if (messageInputRef.current) {
                messageInputRef.current.removeEventListener('click', checkActiveStyles);
                messageInputRef.current.removeEventListener('keyup', checkActiveStyles);
            }
        };
    }, []);

    // Function to handle clicking the new message indicator
    const handleNewMessageIndicatorClick = () => {
        scrollToBottom(messageContainerRef.current);
        setShowNewMessageIndicator(false);
        setNewMessageCount(0);
    };

    // Image paste handler effect (PASTE THIS after all functions/hooks, before return)
    useEffect(() => {
        const input = messageInputRef.current;
        if (!input) return;

        const handlePaste = (e) => {
            // Opinion: handle only if image is found in clipboard
            if (!e.clipboardData) return;
            const items = Array.from(e.clipboardData.items);
            const imageItem = items.find(item => item.type.startsWith('image/'));
            if (imageItem) {
                e.preventDefault();
                const file = imageItem.getAsFile();
                if (file) {
                    setPendingFiles(prev => [...prev, file]);
                }
            }
        };

        input.addEventListener('paste', handlePaste);
        return () => input.removeEventListener('paste', handlePaste);
    }, []);

    return (
            <div
                data-hover-video-id={isSeller ? "om-chat-seller" : "om-chat-buyer"}
             {...getRootProps({
                className: clsx(styles.chatWithVendor,isDragActive && styles.dropActive),
            })}
            >
                <div className={styles.chatHeader}>
                <div>
                    <div className={styles.poNumberMain}>
                        PO#: 
                        <span>
                            {poNumber} {(isSeller && sellerPoNumber) && `(${sellerPoNumber})`}
                        </span>
                    </div>
                    <div className={styles.vendorName}>
                        {companyName}
                        <span className={styles.vendorRole}>
                            {userName}
                        </span>
                    </div>
                </div>
                  {/* <div className={styles.btnCloseContainer}>
                    <button className={styles.closeButton} onClick={close}>
                        <CloseIcon />
                    </button>
                    <button className={styles.expandButton}>
                        <ExpandIcon />
                    </button>
                  </div> */}
                </div>
                <div className={styles.chatBody}>
                    <div className={styles.chatMessages}>
                        <div 
                        className={styles.messageContainer} 
                        ref={messageContainerRef}
                        onScroll={handleScroll}
                        >
                                <input {...getInputProps()} />
                                {messages.map((message) => (
                                    <div key={message.id} className={clsx(styles.message, message.isMyMessage ? styles.myMessage : styles.othersMessage)}>
                                    <div className={styles.messageTimestamp}>{message.formattedTimestamp}</div>
                                    {message.isCombined ? (
                                        // Render combined message as attachment bubble with text
                                        <AttachmentBubble 
                                            name={message.attachments[0].name}
                                            extension={message.attachments[0].extension}
                                            url={message.attachments[0].url}
                                            isMyMessage={message.isMyMessage}
                                            isImgix={isImgix}
                                            text={message.text}
                                            isAdmin={message?.role === "moderator"}
                                        />
                                    ) : message.type === ATTACHMENT ? (
                                        <AttachmentBubble 
                                            name={message.name}
                                            extension={message.extension}
                                            url={message.url}
                                            isMyMessage={message.isMyMessage}
                                            isImgix={isImgix}
                                            isAdmin={message?.role === "moderator"}
                                        />
                                    ) : (
                                        <MessageBubble 
                                            text={message.text} 
                                            isMyMessage={message.isMyMessage} 
                                            isAdmin={message?.role === "moderator"}
                                        />
        )}
    </div>
))}
                             {isDragActive && (
                                    <div className={styles.dropOverlay}>
                                        Drop files to upload
                                    </div>
                                )}
                        </div>
                        {showNewMessageIndicator && (
                            <div 
                                className={styles.newMessageIndicator}
                                onClick={handleNewMessageIndicatorClick}
                            >
                                <DownArrowIcon className={styles.downArrowIcon} />
                                {newMessageCount > 1 
                                    ? `${newMessageCount} new messages` 
                                    : '1 new message'}
                            </div>
                        )}
                    </div>
                </div>
                <div className={styles.inputSection}>
                    <div className={styles.inputContainer}>
                    <div
                        ref={messageInputRef}
                        className={styles.messageInput}
                        contentEditable={!isSendingMessage}
                        onKeyDown={handleKeyDown}
                        data-disabled={isSendingMessage}
                        data-placeholder={isSendingMessage ? "Sending..." : "Type your message..."}
                    ></div>
                    </div>
                    {pendingFiles.length > 0 && (
    <div className={styles.pendingFilesIndicator}>
        {visibleFiles.map(file => <Chip sx={{
            color: '#a0a0a0',
            fontSize: '10px',
            minWidth: 0,
            flexShrink: 0,
            opacity: isSendingMessage ? 0.5 : 1,
            '& .MuiChip-deleteIcon': {
                color: '#a0a0a0',
                fontSize: '12px',
                '&:hover': {
                    color: '#a0a0a0',
                }
            }
        }} size='small' onDelete={()=>setPendingFiles(pendingFiles.filter(f=>f.name !== file.name))} label={shortenFileName(file.name)} />)}
        {remainingCount > 0 && <Tooltip title={
            <ul style={{
                listStyle: 'none',
                padding: 0,
                margin: 0,
            }}>
                {remainingFiles.map(file => <li key={file.name}>{shortenFileName(file.name, 15)}</li>)}
            </ul>
        }>
            <Chip sx={{
            color: '#a0a0a0',
            opacity: isSendingMessage ? 0.5 : 1,
            fontSize: '10px',
            minWidth: 0,
            flexShrink: 0,
        }} size='small' label={`+${remainingCount} more`} />
            </Tooltip>}
    </div>
)}
                    <div>
                        <div className={styles.formattingToolbarContainer}>
                            <div className={styles.formattingToolbar}>
                                <div className={styles.emojiContainer}>
                                    <button
                                        type="button"
                                        className={clsx(styles.formattingButton, showEmojiPicker && styles.emojiButton)}
                                        onClick={handleEmojiButtonClick}
                                        disabled={isSendingMessage}
                                        aria-label="Insert emoji"
                                    >
                                        <EmojiIcon className={styles.Icon} />  <EmojiIconHover className={styles.IconHover} />
                                    </button>
                                    {showEmojiPicker && (
                                        <div className={styles.emojiPicker}>
                                            {commonEmojis.map((emoji, index) => (
                                                <button
                                                    key={index}
                                                    type="button"
                                                    className={styles.emojiOption}
                                                    disabled={isSendingMessage}
                                                    onClick={() => insertEmoji(emoji)}
                                                >
                                                    {emoji}
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.bold && styles.buttonClicked)}
                                    onClick={() => handleFormat('bold')}
                                    disabled={isSendingMessage}
                                    aria-label="Bold text"
                                >
                                    <BoldIcon className={styles.Icon} />  <BoldIconHover className={styles.IconHover} />
                                </button>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.italic && styles.buttonClicked)}
                                    onClick={() => handleFormat('italic')}
                                    disabled={isSendingMessage}
                                    aria-label="Italic text"
                                >
                                    <ItalicIcon className={styles.Icon} />  <ItalicIconHover className={styles.IconHover} />
                                </button>
                                <button
                                    type="button"
                                    className={clsx(styles.formattingButton, activeButtons.underline && styles.buttonClicked)}
                                    onClick={() => handleFormat('underline')}
                                    disabled={isSendingMessage}
                                    aria-label="Underline text"
                                >
                                    <UnderlineIcon className={styles.Icon} />  <UnderlineIconHover className={styles.IconHover} />
                                </button>
                                 <button
                                    type="button"
                                    disabled={isSendingMessage}
                                    className={clsx(styles.formattingButton, activeButtons.underline && styles.buttonClicked)}
                                    onClick={openFileDialog}
                                    aria-label="attachment"
                                >
                                    <AttachmentIcon className={clsx(styles.attachmentIcon,styles.Icon)} />  <AttachmentHoverIcon className={clsx(styles.attachmentIcon,styles.IconHover)} />
                                </button>
                            </div>
    
                            <div className={styles.buttonContainer}>
                                <button className={styles.sendButton} disabled={isSendingMessage} onClick={handleSendMessage}>send</button>
                            </div>
                        </div>
                    </div>
    
                </div>
    
            </div>
        )
}

export default ChatWithVendor
