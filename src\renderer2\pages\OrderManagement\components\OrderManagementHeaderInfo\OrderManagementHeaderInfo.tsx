import { commomKeys, dateTimeFormat, InstantPurchasingWrapper, mobileDiaglogConst, noIdGeneric, orderType, useBuyerSettingStore, useCreatePoStore, useGetDeliveryDate, useGlobalStore, useOrderManagementStore, userRole, useStateZipValidation } from '@bryzos/giss-ui-library';
import React, { useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import { disputeCounterStatus, localStorageKeys, navigationConfirmMessages, routes } from 'src/renderer2/common';
import clsx from 'clsx';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { Autocomplete, ClickAwayListener, Dialog, Fade, IconButton, TextField, Tooltip } from '@mui/material';
import { Controller } from 'react-hook-form';
import StateDropDown from 'src/renderer2/component/StateDropDown/StateDropDown';
import { ReactComponent as ChooseOneIcon } from 'src/renderer2/assets/New-images/Choose-One.svg';
import { ReactComponent as UploadBOMIcon } from 'src/renderer2/assets/New-images/Upload-BOM.svg';
import { ReactComponent as UploadBOMIconHover } from 'src/renderer2/assets/New-images/Bom-Upload-Hover.svg';
import { ReactComponent as Shape1 } from 'src/renderer2/assets/New-images/New-Image-latest/shape1.svg';
import { ReactComponent as Shape2 } from 'src/renderer2/assets/New-images/New-Image-latest/shape2.svg';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import dayjs from 'dayjs';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { poHeaderSchema } from 'src/renderer2/models/poHeader.model';
import useMutateGetDeliveryAddress from 'src/renderer2/hooks/useMutateGetDeliveryAddress';
import { convertUtcToLocalTime, formatDisplayDateForTemplate, formatDisputePayload, getLocal, setLocal } from 'src/renderer2/helper';
import usePostPoUpdatePricing from 'src/renderer2/hooks/usePostPoUpdatePricing';
import ShipmentsTab from 'src/renderer2/pages/buyer/newSettings/tabs/ShipmentsTab';
import styles from './OrderManagementHeaderInfo.module.scss';
import { ReactComponent as PointRightIcon } from '../../../../assets/New-images/PointRight.svg';
import usePostDisputeOrder from 'src/renderer2/hooks/usePostDisputeOrder';

interface OrderManagementHeaderInfoRef {
    watch: any;
    setValue: any;
}

const OrderManagementHeaderInfo = forwardRef<OrderManagementHeaderInfoRef, any>((
    {
        formInputGroupRef,
        isCalendarOpen,
        setIsCalendarOpen,
        setOpenErrorDialog,
        setErrorMessage,
        saveBomHeaderDetails,
        disableBidBuyNow,
        setOpenDeliveryToDialog,
        openDeliveryToDialog,
        scrollToTop,
        isSavedBom,
        focusJobPoInput,
        setCameFromSavedBom,
        saveUserActivity,
        HeaderDetailsConfirmedRef,
        isHeaderDetailsConfirmed,
        setIsHeaderDetailsConfirmed,
        cameFromSavedBom,
        handleStoreUndoStack,
        isOrderLineChanges,
        setIsStateZipValChange,
        componentType,
        currentFocusedItem,
        removeFromAttentionItems
    }, ref) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { setShowLoader, backNavigation, setCreatePoSessionId, resetHeaderConfig, userData, setBackNavigation, bryzosPayApprovalStatus, setBryzosPayApprovalStatus, referenceData, productData, productMapping, discountData, referenceDataUpdated } = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, isCreatePOModule, setIsCreatePOModule, setIsCreatePoDirty, isCreatePoDirty, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, setBomDataIdToRefresh, bomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData } = useCreatePoStore();
    const orderInfoIsFilled = useCreatePoStore((state: any) => state.orderInfoIsFilled);
    const setOrderInfoIsFilled = useCreatePoStore((state: any) => state.setOrderInfoIsFilled);
    const selectedQuote = useCreatePoStore((state: any) => state.selectedQuote);
    const setSelectedQuote = useCreatePoStore((state: any) => state.setSelectedQuote);
    const quoteList = useCreatePoStore((state: any) => state.quoteList);
    const setQuoteList = useCreatePoStore((state: any) => state.setQuoteList);
    const purchasingList = useCreatePoStore((state: any) => state.purchasingList);
    const setPurchasingList = useCreatePoStore((state: any) => state.setPurchasingList);
    const isEditingPo = useOrderManagementStore((state: any) => state.isEditingPo);
    const setIsEditingPo = useOrderManagementStore((state: any) => state.setIsEditingPo);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setOpenLeftPanel, setDisplayLeftPanel } = useLeftPanelStore();
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const containerRef = useRef(null);
    const fileInputRef = useRef(null); // Add ref for file input
    const [isFocused, setIsFocused] = useState(false);
    const [stateInputFocus, setStateInputFocus] = useState(false);
    const [autocompleteOpen, setAutocompleteOpen] = useState(false);
    const [autocompleteOpenLine2, setAutocompleteOpenLine2] = useState(false);
    const [autocompleteOpenCity, setAutocompleteOpenCity] = useState(false);
    const { mutateAsync: getDeliveryAddresses, data: deliveryAddressData } = useMutateGetDeliveryAddress();
    const [stateDropDownValue, setStateDropDownValue] = useState<any>('');
    const { buyerSetting } = useBuyerSettingStore();
    const getDeliveryDate = useGetDeliveryDate();
    const [deliveryDateMap, setDeliveryDateMap] = useState({});
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [disableDeliveryDate, setDisableDeliveryDate] = useState(false);
    const [states, setStates] = useState([]);
    const [addressDialogOpen, setAddressDialogOpen] = useState(false);
    const [initialPoData, setInitialPoData] = useState({});
    const jobPoInputRef = useRef(null);
    const [undoStackObject, setUndoStackObject] = useState({});
    const [isDataChanged, setIsDataChanged] = useState(false);
    const [autoFocusInDeliveryAddress, setAutoFocusInDeliveryAddress] = useState(false);
    const {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        errors, 
        isValid
    } = useGenericForm(poHeaderSchema);

    const checkStateZipValidation = useStateZipValidation();
    const { mutateAsync: updatePricing } = usePostPoUpdatePricing();
    const { mutateAsync: disputeOrderMutation } = usePostDisputeOrder();
    const isOrderManagementPage = componentType === 'ORDER' || location.pathname === routes.orderManagementPage;
    const isQuotePage = componentType === 'QUOTE' || location.pathname === routes.quotePage;
    const isPurchaseOrderPage = componentType === 'PO' || location.pathname === routes.createPoPage;

    useEffect(() => {
        setOrderInfoIsFilled(
            !!watch('shipping_details.line1') &&
            !!watch('shipping_details.city') &&
            !!watch('shipping_details.state_id') &&
            !!watch('shipping_details.zip') &&
            !!watch('buyer_internal_po') &&
            (watch('order_type') === orderType.QUOTE || !!watch('delivery_date')) &&
            !errors?.shipping_details?.zip?.message
        )
    }, [
        watch('shipping_details.line1'),
        watch('shipping_details.city'),
        watch('shipping_details.state_id'),
        watch('shipping_details.zip'),
        watch('buyer_internal_po'),
        watch('order_type'),
        watch('delivery_date'),
        errors?.shipping_details?.zip?.message
    ]);

    const getDeliveryDateData = async (deliveryAddressId: string | null) => {
        try{
            const selectedQuote = useCreatePoStore.getState().selectedQuote;
            // if(!deliveryAddressId){
            //     showCommonDialog(null, "Please select a delivery address", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            //     return []
            // }
            let deliveryDateList = [];
            
            const payload = {
                id: deliveryAddressId,
                timestamp: selectedQuote && dayjs(selectedQuote?.created_date).format(dateTimeFormat.isoDateTimeWithTFormat)
            }
            const res = await getDeliveryDate.mutateAsync(payload);
            if (res?.data?.data) {
                deliveryDateList = res.data.data
            }
            const optionMap = deliveryDateList.reduce((acc, option) => {
                acc[option.value] = option;
                return acc;
            }, {});
            setDeliveryDateMap(optionMap)
            setDeliveryDates(deliveryDateList);
            let disableDeliveryDates = true;
            deliveryDateList.forEach(deliveryDateObj => {
                // if(deliveryDateObj.days_to_add === deliveryDaysAddValue){
                //     const deliveryDate = !deliveryDateObj.disabled ? deliveryDateObj.value : null;
                //     setValue('delivery_date', dayjs(deliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit));
                // }
                if (!deliveryDateObj.disabled && disableDeliveryDates) {
                    disableDeliveryDates = false;
                }
            });
            setDisableDeliveryDate(disableDeliveryDates)
            return optionMap
        }catch(error){
            console.error("err @>>>>>>>", error)
            return []
        }
    }

    const handleUndoForHeader = (lastElement: any) => {
        if(lastElement?.multipleData){
            Object.keys(lastElement.multipleData).forEach((key: any) => {
                setValue(key, lastElement.multipleData[key]);
            });
        } else {
            setValue(lastElement.name, lastElement.value);
            if (lastElement.name.includes("shipping_details")){
                setAutoFocusInDeliveryAddress(true);
                handleDeliveryToClick();
            }
        }
        if(lastElement.id === "shipping_details.state_id"){
            setStateDropDownValue(lastElement.multipleData['shipping_details.state_code']);
        }
        setIsCreatePoDirty(true);
        saveBomHeaderDetails();
        setTimeout(() => {
            const ele = document.getElementById(`${lastElement.id}`);
            if (ele) {
                ele.focus();
            }
        }, 200)
    }

    // Expose orderInfoIsFilled to parent component
    useImperativeHandle(ref, () => ({
        initializePoHeaderForm,
        getHeaderFormData,
        watch,
        setValue,
        handleUndoForHeader
    }), [watch, setValue]);

    useEffect(() => {
        if(focusJobPoInput){
            setTimeout(() => {
                jobPoInputRef?.current?.focus();
            }, 100)
        }
    }, [focusJobPoInput])

    useEffect(() => {
        if(referenceData){
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        setValue('isEdit', true);
        getDeliveryAddresses();
        function handleClickOutside(event) {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsFocused(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
            const orderTypeData = isPurchaseOrderPage ? orderType.PO : orderType.QUOTE;
            reset();
            setValue('buyer_internal_po', '');
            setValue('delivery_date', '');
            setValue('shipping_details', {});
            setValue('order_type', isOrderManagementPage ? '' : orderTypeData);
            setValue('delivery_date_offset', '');
            setValue('isEdit', true);
            setValue('id', undefined);
            setStateDropDownValue('');
        };
    }, []);

    useEffect(() => {
        if(watch('shipping_details.zip') && watch('shipping_details.state_id')){
            onStateZipChange()
        }
    }, [watch('shipping_details.zip'), watch('shipping_details.state_id')])

    useEffect(() => {
        if(buyerSetting)setRecevingHoursAndDeliveryDateOffset()
    }, [buyerSetting])

    useEffect(()=>{
        if(!selectedQuote || selectedQuote?.length === 0){
            const orderTypeData = isPurchaseOrderPage ? orderType.PO : orderType.QUOTE;
            reset();
            setValue('buyer_internal_po', '');
            setValue('delivery_date', '');
            setValue('shipping_details', {});
            setValue('order_type', isOrderManagementPage ? '' : orderTypeData);
            setValue('delivery_date_offset', '');
            setValue('isEdit', true);
            setValue('id', undefined);
            setStateDropDownValue('');
        }
    },[selectedQuote])

    useEffect(() => {
        if(isEditingPo){
            setValue('isEdit', isEditingPo);
        }
    }, [isEditingPo])

    const handleJobPoInputRef = (e: any) => {
        jobPoInputRef.current = e;
    }

    const initializePoHeaderForm = async (initialData: any) => {
        setInitialPoData(initialData);
        const deliveryAddressDataFromSetting = buyerSetting?.delivery_address || [];
        // if(deliveryAddressDataFromSetting?.length === 0 || deliveryAddressDataFromSetting?.user_delivery_receiving_availability_details?.length === 0) return;
        const defaultDeliveryAddress = deliveryAddressDataFromSetting?.find((address: any) => address.is_default) || deliveryAddressDataFromSetting[0];

        
        const deliveryMap =  await getDeliveryDateData(defaultDeliveryAddress?.id || undefined);
        if(initialData && 'isEdit' in initialData){
            setValue('isEdit', initialData?.isEdit);
        }else{
            setValue('isEdit', true);
        }
        if(initialData){
            if(initialData?.cameFromQuote){
                setCameFromSavedBom(true);
            }
            setValue('id', initialData.id);
            setValue('buyer_internal_po', initialData.buyer_internal_po);
            if('pricing_expired' in initialData){
                setValue('pricing_expired', initialData.pricing_expired);
            }
            setValue('shipping_details', initialData.shipping_details);
            if(!initialData?.shipping_details?.delivery_address_id && location.pathname !== routes.orderManagementPage){
                setValue('shipping_details.delivery_address_id', defaultDeliveryAddress?.id);
            }
            setValue('delivery_date', initialData.delivery_date);
            setValue('order_type', initialData.order_type);
            if(initialData?.delivery_date_offset) {
                setValue('delivery_date_offset', initialData.delivery_date_offset)
            }else{
                const selectedDate = dayjs(initialData.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
                setValue('delivery_date_offset', deliveryMap?.[selectedDate]?.days_to_add);
            }
            setStateDropDownValue(referenceData?.ref_states?.find((state: any) => state.id === initialData?.shipping_details?.state_id)?.code || '')
        }
    }

    const onStateZipChange = async () => {
        await handleStateZipValidation();
    }

    const setRecevingHoursAndDeliveryDateOffset = () => {
        const deliveryAddressDataFromSetting = buyerSetting?.delivery_address || [];
        // if(deliveryAddressDataFromSetting?.length === 0 || deliveryAddressDataFromSetting?.user_delivery_receiving_availability_details?.length === 0){
        //     setAddressDialogOpen(true);
        //     return;
        // }
        const defaultDeliveryAddress = deliveryAddressDataFromSetting?.find((address: any) => address.is_default) || deliveryAddressDataFromSetting[0];
        getDeliveryDateData(defaultDeliveryAddress?.id || undefined);
        const deliveryReceivingHours = defaultDeliveryAddress?.user_delivery_receiving_availability_details;
        if (deliveryReceivingHours?.length !== 0) {
            setValue('recevingHours', deliveryReceivingHours)
        }
        const deliveryDaysAddValue = defaultDeliveryAddress?.delivery_days_add_value ?? referenceData?.ref_delivery_date[0]?.days_to_add;
        setValue('delivery_date_offset', deliveryDaysAddValue);
    }

    const getHeaderFormData = () => {
        return {
            buyer_internal_po: watch('buyer_internal_po'),
            shipping_details: watch('shipping_details'),
            delivery_date: watch('delivery_date'),
            order_type: watch('order_type')
        }
    }

    const handleStateZipValidation = async (): Promise<Boolean> => {
        const zipCode = watch("shipping_details.zip");
        const stateCode = watch("shipping_details.state_id");
        let isValid:Boolean = false;
        if (zipCode) {
          setValue('shipping_details.validating_state_id_zip', true);
            if (zipCode.length > 4 && stateCode) {
                const checkStateZipResponse: any = await checkStateZipValidation.mutateAsync({ zipCode, stateCode: Number(stateCode) });
                 if (checkStateZipResponse.data.data === true) {
                    clearErrors(["shipping_details.zip","shipping_details.state_id"]);
                    isValid = true;
                } else {
                    showStateZipError();
                }
            } else {
                setError("shipping_details.zip", { message: "Zip Code is not valid" });
            }
          setValue('shipping_details.validating_state_id_zip', false);
        }
        return isValid;
      };

      const showStateZipError = () => {
        const zipCode = "shipping_details.zip";
        const stateCode = "shipping_details.state_id";
        setError(stateCode, { message: "The State and Zipcode do not match." });
        setError(zipCode, { message: "The State and Zipcode do not match." });
      }

    const handleOpenCalendarBtn = () => {
        setUndoStackObject({name: "delivery_date", value: watch('delivery_date'), id: "delivery_date", from: "header"});
        if (!disableDeliveryDate && watch('isEdit')) {
            setIsCalendarOpen(true);
        } else {
            if(disableDeliveryDate){
                setOpenErrorDialog(true)
                setErrorMessage(mobileDiaglogConst.receivingHrsEmpty)
            }
            setIsCalendarOpen(false)
            const nextElement = document.querySelector('[tabindex="15"]');
            if (nextElement instanceof HTMLElement) {
                nextElement.focus();
            }
        }
    }


    const allowedDates = useMemo(() => {
        return deliveryDates
            .filter(date => !date?.disabled)
            .map(date => new Date(date?.value));
    }, [deliveryDates]);

    const handleDateSelect = (date: any) => {
        const selectedDate = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
        setValue('delivery_date_offset', deliveryDateMap?.[selectedDate]?.days_to_add);
        setIsCreatePoDirty(true)
        handleStoreUndoStack({...undoStackObject, currentValue: selectedDate, uniqueId: "delivery_date"});
    }


    const handleDeliveryInfoContainerClickAway = () => {
        if(autoFocusInDeliveryAddress){
            setAutoFocusInDeliveryAddress(false);
            return;
        }
        if(!watch('isEdit')){
            return;
        }
        if (errors?.shipping_details?.zip?.message || errors?.shipping_details?.state_id?.message || errors?.shipping_details?.city?.message || errors?.shipping_details?.line1?.message || errors?.shipping_details?.line2?.message) {
            setOpenDeliveryToDialog(true)
            setIsFocused(true)
        } else {
            setStateInputFocus(false)
            setOpenDeliveryToDialog(false)
            setIsFocused(false)
        }
    }

    const handleDeliveryToClick = () => {
        if(!watch('isEdit')){
            return;
        }
        setOpenDeliveryToDialog(true);
        setIsFocused(true);
    }

    const handleAutocompleteTabSelection = (
        e: any,
        field: any,
        isOpen: boolean,
        filterFields: string[],
        setOpenState: (open: boolean) => void,
        nextFieldSelector: string
    ) => {
        if (e.key === 'Tab' && !e.shiftKey) {
            // Handle Tab key to select highlighted option
            if (isOpen && deliveryAddressData?.length > 0) {
                e.preventDefault();
                const activeElement = document.activeElement;
                const activeDescendantId = activeElement?.getAttribute('aria-activedescendant');

                if (activeDescendantId) {
                    // Extract option index from the ID
                    const optionIndexMatch = activeDescendantId.match(/option-(\d+)$/);
                    if (optionIndexMatch && optionIndexMatch[1]) {
                        const optionIndex = parseInt(optionIndexMatch[1]);
                        // Get filtered options based on current input
                        const inputValue = field.value || '';
                        const filteredOptions = deliveryAddressData.filter(option => {
                            return filterFields.some(filterField => {
                                const fieldValue = option?.[filterField];
                                return fieldValue?.toLowerCase().includes(inputValue.toLowerCase());
                            });
                        });

                        // Select the highlighted option
                        if (optionIndex >= 0 && optionIndex < filteredOptions.length) {
                            const selectedOption = filteredOptions[optionIndex];
                            if(selectedOption.id !== watch('shipping_details.delivery_address_id')){
                                getDeliveryDateData(selectedOption.id)
                            }
                            // Auto-populate all shipping details fields
                            setValue('shipping_details.delivery_address_id', selectedOption.id);
                            setValue('shipping_details.line1', selectedOption.line1);
                            setValue('shipping_details.line2', selectedOption.line2 || '');
                            setValue('shipping_details.city', selectedOption.city);
                            setValue('shipping_details.state_id', selectedOption.state_id);
                            setValue('shipping_details.zip', selectedOption.zip);
                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                            setStateDropDownValue(stateName);
                            setIsCreatePoDirty(true);
                            saveBomHeaderDetails();
                            setOpenState(false);

                            // Focus on next field after selection
                            setTimeout(() => {
                                const nextInput = document.querySelector(nextFieldSelector);
                                if (nextInput instanceof HTMLElement) {
                                    nextInput.focus();
                                }
                            }, 100);
                        }
                    }
                }
            }
        }
    };

    const formatAddressDisplay = (address: any) => {
        if (!address) return '';
        const stateName = referenceData?.ref_states?.find(state => state.id === address?.state_id)?.code || '';

        const parts = [address.line1];
        if (address.line2 && address.line2.trim()) {
            parts.push(address.line2);
        }

        parts.push(address.city);
        parts.push(stateName);
        parts.push(address.zip);

        return parts.filter(Boolean).join(', ');
    };


    const handleUploadClick = () => {
        if(!watch('isEdit')){
            return;
        }
        if (orderInfoIsFilled) {
            const { delivery_date, shipping_details, order_type, buyer_internal_po, delivery_date_offset, id } = getValues();
            const formattedCreatePoData = {
                delivery_date,
                shipping_details,
                order_type,
                buyer_internal_po,
                delivery_date_offset,
                id
            }
            // setCreatePoData(formattedCreatePoData)
            setUploadBomInitialData(formattedCreatePoData)
            setUpdatedDraftId(selectedQuote);
            const isDirty = selectedQuote?.cart_items?.some(item => item.descriptionObj && Object.keys(item.descriptionObj).length > 0);
            const message = navigationConfirmMessages.unsavedChanges;
            if (isDirty) {
                showCommonDialog(null, message, null, resetDialogStore, [{ name: 'Yes', action: handleFileInputClick }, { name: 'No', action: resetDialogStore }])
            }
            else {
                handleFileInputClick();
            }
        }
    };

    const handleFileInputClick = () => {
        fileInputRef.current?.click();
        resetDialogStore();
    }

    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            if (!file.type.includes('pdf')) {
                showCommonDialog(null, "Please select a PDF file.", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                return;
            }
            if(selectedQuote?.id && selectedQuote?.cart_items?.length > 0){
                const updatedCartItems = selectedQuote?.id.includes(noIdGeneric) ? null : selectedQuote?.cart_items?.map((item: any) => {
                    return {
                        ...item,
                        descriptionObj: {}
                    }
                })
                const _selectedQuote = {
                    ...selectedQuote,
                    cart_items: updatedCartItems
                }
                setSelectedQuote(_selectedQuote);
                if (_selectedQuote.order_type === orderType.QUOTE) {
                    setLocal(localStorageKeys.poQuoting, _selectedQuote);
                    const _quoteList = quoteList.map((quote: any) => {
                        if (quote.id === _selectedQuote.id) {
                            return _selectedQuote;
                        }
                        return quote;
                    });
                    setQuoteList(_quoteList);
                } else {
                    setLocal(localStorageKeys.poPurchasing, _selectedQuote);
                    const _purchasingList = purchasingList.map((quote: any) => {
                        if (quote.id === _selectedQuote.id) {
                            return _selectedQuote;
                        }
                        return quote;
                    });
                    setPurchasingList(_purchasingList);
                }
            }
            navigate(routes.bomExtractor, { state: { file } });
        }
    };

    const handleOnSuccessShippingDetails = async () => {
        setAddressDialogOpen(false);
    }

    
    const handleCloseConfirmPoHeader = () => {
        // setShowConfirmPoHeaderDialog(false);
        setIsHeaderDetailsConfirmed(true);
    }

    const handleSaveConfirmPoHeader = (headerData: any) => {
        // Handle saving the confirmed header data
        console.log('Saving confirmed header data:', headerData);
        setValue('buyer_internal_po', headerData.buyer_internal_po);
        setValue('delivery_date', headerData.delivery_date);
        setValue('shipping_details', headerData.shipping_details);
        setValue('order_type', headerData.order_type);
        setValue('delivery_date_offset', headerData.delivery_date_offset);
        setValue('isEdit', headerData.isEdit);
        setValue('id', headerData.id);
        setIsCreatePoDirty(true);
        saveBomHeaderDetails();
        // const getLocalPurchasingData = getLocal(localStorageKeys.poPurchasing, null);
        // if(getLocalPurchasingData){
        //     setLocal(localStorageKeys.poPurchasing, {...getLocalPurchasingData, ...watchData, ...headerData});
        // }else{
        //     setLocal(localStorageKeys.poPurchasing, {...watchData, ...headerData});
        // }
        setIsHeaderDetailsConfirmed(true);
        // setShowConfirmPoHeaderDialog(false);
        // Add any additional save logic here
    }

    const getHeaderDataForConfirm = () => {
        // Return the current header form data for confirmation
        return {
            // Add the relevant header data fields here
            // This should match what the ConfirmPoHeaderDetails component expects
        };
    }

    useEffect(() => {
        const wrapperContentOverlay = document.getElementById('wrapperContentOverlay');
        if(!isHeaderDetailsConfirmed && cameFromSavedBom && wrapperContentOverlay){
            wrapperContentOverlay.style.right = `0`;
            wrapperContentOverlay.style.bottom = `0`;
        }
        else{
            if(wrapperContentOverlay){
                wrapperContentOverlay.style.right = `unset`;
                wrapperContentOverlay.style.bottom = `unset`;
            }
        }
    }, [isHeaderDetailsConfirmed ,cameFromSavedBom]);

    const handleAcceptCounter = async() => {
        try{
            const lastCounterData = initialPoData?.order_level_dispute?.deliver_by[initialPoData?.order_level_dispute?.deliver_by.length - 1];
            const payload = formatDisputePayload("orderLevel", "deliver_by", {
                delivery_date: lastCounterData?.delivery_date,
                counter_id: lastCounterData?.counter_id
            }, "accept");
            console.log("payload @>>>>>>>", payload);
            const response = await disputeOrderMutation(payload);
            if (response?.data?.error_message) {
                showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                return;
            }
            removeFromAttentionItems("deliver_by", "order_level");
            console.log("lastCounterData @>>>>>>>", lastCounterData);
        } catch(error){
            console.log("handleAcceptCounter error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }

    }
    const handleCounterClick = () => {
        setIsCalendarOpen(true);
        const lastCounterData = initialPoData?.order_level_dispute?.deliver_by[initialPoData?.order_level_dispute?.deliver_by.length - 1];
        const payload = formatDisputePayload("orderLevel", "deliver_by", {
            delivery_date: lastCounterData?.delivery_date,
            counter_id: lastCounterData?.counter_id,
        }, "counter");
        removeFromAttentionItems("deliver_by", "order_level");
        console.log("lastCounterData @>>>>>>>", lastCounterData);
        console.log("payload @>>>>>>>", payload);
    }
    const handleRejectCounter = async() => {
        try{
            const lastCounterData = initialPoData?.order_level_dispute?.deliver_by[initialPoData?.order_level_dispute?.deliver_by.length - 1];
            const payload = formatDisputePayload("orderLevel", "deliver_by", {
                delivery_date: lastCounterData?.delivery_date,
                counter_id: lastCounterData?.counter_id,
            }, "reject");
            console.log("payload @>>>>>>>", payload);
            const response = await disputeOrderMutation(payload);
            if (response?.data?.error_message) {
                showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                return;
            }
            removeFromAttentionItems("deliver_by", "order_level");
            console.log("lastCounterData @>>>>>>>", lastCounterData);
        } catch(error){
            console.log("handleRejectCounter error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    }
    const handleOpenCalendar = () => {
        setIsCalendarOpen(true);
    };

    const handleClickAway = () => {
        setIsCalendarOpen(false);
    };

    return (
        <div className={clsx(styles.headerInfoContainer, (isCalendarOpen && watch('isEdit')) && styles.isCalendarOpenDiabledInput)} ref={formInputGroupRef} data-hover-video-id='create-po-header'>
            {(isCalendarOpen) && <div className={styles.calendarOpenOverlay}></div>}
            <div className={clsx(styles.createPoHeaderInfoGrid, !watch('isEdit') && styles.isEditMode)}>
                <div className={clsx(styles.leftGridHeader)}>
                    <div className={clsx(styles.formInputGroup1, styles.formInputHeaderTop)}>
                        <div className={clsx(styles.col1, styles.poInputMain, styles.orderManagementPoName, watch('buyer_internal_po') && styles.hasValue)}>
                            <div className={styles.poNameLabel}>PO Name<span>:</span></div>
                            {isEditingPo ? (
                                <InputWrapper>
                                    <CustomTextField className={clsx(styles.inputfiled, styles.pOInput)} type='text' register={register("buyer_internal_po")}
                                        placeholder='JOB / PO#'
                                        onBlur={(e) => {
                                            e.target.value = e.target.value.trim();
                                            register("buyer_internal_po").onBlur(e);
                                            saveUserActivity();
                                            if ((!getLocal(localStorageKeys.poQuoting, null) && e.target.value) || (getLocal(localStorageKeys.poQuoting, null) && e.target.value !== getLocal(localStorageKeys.poQuoting, null)?.buyer_internal_po)) {
                                                saveBomHeaderDetails();
                                            }
                                            if (isDataChanged) {
                                                handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, uniqueId: "buyer_internal_po" });
                                                setUndoStackObject({});
                                                setIsDataChanged(false);
                                            }
                                        }}
                                        onChange={(e) => {
                                            setIsCreatePoDirty(true);
                                            setIsDataChanged(true);
                                        }}
                                        onFocus={() => {
                                            setUndoStackObject({ name: "buyer_internal_po", value: watch('buyer_internal_po'), id: "buyer_internal_po", from: "header" });
                                        }}
                                        maxLength={20}
                                        tabIndex={13}
                                        inputRef={handleJobPoInputRef}
                                        autoFocus={location.pathname !== routes.savedBom}
                                        disabled={!watch('isEdit')}
                                        id="buyer_internal_po"
                                    />
                                </InputWrapper>
                            ) : (
                                <div className={styles.pOInputValue}>
                                    {watch('buyer_internal_po')}
                                </div>
                            )}
                        </div>
                    </div>
                    {!initialPoData?.order_level_dispute?.deliver_by && (
                        <div className={clsx(styles.formInputGroup1, styles.formInputHeaderTop)}>
                            <div className={clsx(styles.deliverByContainer, styles.col1, styles.orderManagementPoName)}>
                                <div className={styles.poNameLabel}>Delivery By<span>:</span></div>
                                {isEditingPo ? (
                                    <Calendar allowedDates={allowedDates}
                                        // value={dayjs(watch('delivery_date'), "M/D/YYYY @ hh:mm A").format('M/D/YY')}
                                        value={watch('delivery_date')}
                                        setValue={setValue}
                                        isCalendarOpen={isCalendarOpen}
                                        setIsCalendarOpen={setIsCalendarOpen}
                                        disableDeliveryDate={disableDeliveryDate || !watch('isEdit')}
                                        handleOpenCalendar={handleOpenCalendarBtn}
                                        saveUserActivity={saveUserActivity}
                                        onDateSelect={handleDateSelect}
                                        saveBomHeaderDetails={saveBomHeaderDetails}
                                        OrderCalendarOpen={setIsCalendarOpen}

                                    />
                                ) : (
                                    <div className={styles.pOInputValue}>{dayjs(watch('delivery_date')).format('dddd, MMMM D, YYYY')}</div>
                                )}
                            </div>
                        </div>
                    )}
                    <div className={clsx(styles.formInputGroup1, styles.orderManagementPoName)}>
                        <div className={styles.col1}>
                            <div className={styles.poNameLabel}>Delivery To<span>:</span></div>
                            {isEditingPo ? (
                                <ClickAwayListener onClickAway={() => handleDeliveryInfoContainerClickAway()}>
                                    <div
                                        className={`${styles.deliverToContainer} ${isFocused ? styles.boxShadow : styles.notEditMode}`}
                                        ref={containerRef}
                                        onClick={handleDeliveryToClick}
                                        tabIndex={17}
                                        onFocus={handleDeliveryToClick}
                                    >
                                        {(openDeliveryToDialog) ?
                                            <span className={styles.deliverToLabel}>
                                                <>
                                                    <Controller
                                                        name="shipping_details.line1"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <Autocomplete
                                                                disableClearable
                                                                className={clsx(styles.autocompleteContainer, styles.line1Input)}

                                                                options={((isOrderLineChanges && initialPoData?.seller_name) ? [] : deliveryAddressData) || []}
                                                                value={null}
                                                                inputValue={field.value || ''}
                                                                open={autocompleteOpen && (field.value?.length || 0) > 0}
                                                                onOpen={() => {
                                                                    if ((field.value?.length || 0) > 0) {
                                                                        setAutocompleteOpen(true);
                                                                    }
                                                                }}
                                                                onClose={() => setAutocompleteOpen(false)}
                                                                getOptionLabel={(option) => {
                                                                    if (typeof option === 'string') return option;
                                                                    if (!option) return '';
                                                                    return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                                }}
                                                                isOptionEqualToValue={(option, value) => {
                                                                    return option?.id === value?.id;
                                                                }}
                                                                filterOptions={(options, { inputValue }) => {
                                                                    if (!inputValue) return options;
                                                                    const filtered = options.filter(option =>
                                                                        option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                        option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                        option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                                    );
                                                                    return filtered;
                                                                }}
                                                                classes={{
                                                                    paper: styles.autocompleteDropdown
                                                                }}
                                                                onInputChange={(event, newInputValue) => {
                                                                    field.onChange(newInputValue);
                                                                    setIsCreatePoDirty(true);
                                                                    // Control when dropdown opens based on input length
                                                                    if (newInputValue.length > 0) {
                                                                        setAutocompleteOpen(true);
                                                                    } else {
                                                                        setAutocompleteOpen(false);
                                                                    }
                                                                }}
                                                                onChange={(event, selectedOption) => {
                                                                    if (selectedOption && typeof selectedOption === 'object') {
                                                                        // Auto-populate all shipping details fields
                                                                        if (selectedOption.id !== watch('shipping_details.delivery_address_id')) {
                                                                            getDeliveryDateData(selectedOption.id)
                                                                        }
                                                                        setValue('shipping_details.delivery_address_id', selectedOption.id);
                                                                        setValue('shipping_details.line1', selectedOption.line1);
                                                                        setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                        setValue('shipping_details.city', selectedOption.city);
                                                                        setValue('shipping_details.state_id', selectedOption.state_id);
                                                                        setValue('shipping_details.zip', selectedOption.zip);
                                                                        const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                                        setStateDropDownValue(stateName)
                                                                        setIsCreatePoDirty(true);
                                                                        // saveUserActivity();
                                                                        saveBomHeaderDetails();
                                                                    }
                                                                }}
                                                                renderInput={(params) => (
                                                                    <TextField
                                                                        autoFocus
                                                                        {...params}
                                                                        className={clsx(styles.companyNameInput, styles.muiAutocompleteTextField)}
                                                                        type='text'
                                                                        placeholder='ADDRESS 1'
                                                                        onBlur={(e) => {
                                                                            e.target.value = e.target.value.trim();
                                                                            field.onBlur();
                                                                            saveUserActivity();
                                                                            if ((!getLocal(localStorageKeys.poQuoting, null) && e.target.value) || (getLocal(localStorageKeys.poQuoting, null) && e.target.value !== getLocal(localStorageKeys.poQuoting, null)?.shipping_details?.line1)) {
                                                                                saveBomHeaderDetails();
                                                                            }
                                                                            if (isDataChanged) {
                                                                                handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, uniqueId: 'shipping_details' });
                                                                                setUndoStackObject({});
                                                                                setIsDataChanged(false);
                                                                            }
                                                                        }}
                                                                        onChange={(e) => {
                                                                            setIsDataChanged(true);
                                                                        }}
                                                                        onKeyDown={(e) => {
                                                                            if (e.key === 'Tab') {
                                                                                if (e.shiftKey) {
                                                                                    const nextElement = document.querySelector('[tabindex="16"]');
                                                                                    if (nextElement instanceof HTMLElement) {
                                                                                        nextElement.focus();
                                                                                    }
                                                                                    setOpenDeliveryToDialog(false);
                                                                                    setIsFocused(false)
                                                                                } else {
                                                                                    handleAutocompleteTabSelection(
                                                                                        e,
                                                                                        field,
                                                                                        autocompleteOpen,
                                                                                        ['line1', 'line2', 'city'],
                                                                                        setAutocompleteOpen,
                                                                                        'input[name="shipping_details.line2"]'
                                                                                    );
                                                                                }
                                                                            }
                                                                        }}
                                                                        onFocus={() => {
                                                                            setUndoStackObject({ name: "shipping_details.line1", value: watch('shipping_details.line1'), id: "shipping_details.line1", from: "header" });
                                                                        }}
                                                                    />
                                                                )}
                                                                id="shipping_details.line1"
                                                                renderOption={(props, option) => (
                                                                    <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                        <div>
                                                                            {formatAddressDisplay(option)}
                                                                        </div>
                                                                    </li>
                                                                )}
                                                                freeSolo
                                                                clearOnEscape
                                                                noOptionsText=""
                                                            />
                                                        )}
                                                    />
                                                    <Controller
                                                        name="shipping_details.line2"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <Autocomplete
                                                                disableClearable
                                                                options={((isOrderLineChanges && initialPoData?.seller_name) ? [] : deliveryAddressData) || []}
                                                                value={null}
                                                                inputValue={field.value || ''}
                                                                open={autocompleteOpenLine2 && (field.value?.length || 0) > 0}
                                                                onOpen={() => {
                                                                    if ((field.value?.length || 0) > 0) {
                                                                        setAutocompleteOpenLine2(true);
                                                                    }
                                                                }}
                                                                onClose={() => setAutocompleteOpenLine2(false)}
                                                                getOptionLabel={(option) => {
                                                                    if (typeof option === 'string') return option;
                                                                    if (!option) return '';
                                                                    return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                                }}
                                                                isOptionEqualToValue={(option, value) => {
                                                                    return option?.id === value?.id;
                                                                }}
                                                                filterOptions={(options, { inputValue }) => {
                                                                    if (!inputValue) return options;
                                                                    return options.filter(option =>
                                                                        option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                        option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                        option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                                    );
                                                                }}
                                                                onInputChange={(event, newInputValue) => {
                                                                    field.onChange(newInputValue);
                                                                    setIsCreatePoDirty(true);
                                                                    // Control when dropdown opens based on input length
                                                                    if (newInputValue.length > 0) {
                                                                        setAutocompleteOpenLine2(true);
                                                                    } else {
                                                                        setAutocompleteOpenLine2(false);
                                                                    }
                                                                }}
                                                                onChange={(event, selectedOption) => {
                                                                    if (selectedOption && typeof selectedOption === 'object') {
                                                                        // Auto-populate all shipping details fields
                                                                        if (selectedOption.id !== watch('shipping_details.delivery_address_id')) {
                                                                            getDeliveryDateData(selectedOption.id)
                                                                        }
                                                                        setValue('shipping_details.delivery_address_id', selectedOption.id);
                                                                        setValue('shipping_details.line1', selectedOption.line1);
                                                                        setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                        setValue('shipping_details.city', selectedOption.city);
                                                                        setValue('shipping_details.state_id', selectedOption.state_id);
                                                                        setValue('shipping_details.zip', selectedOption.zip);
                                                                        const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                                        setStateDropDownValue(stateName);
                                                                        setIsCreatePoDirty(true);
                                                                        saveBomHeaderDetails();
                                                                    }
                                                                }}
                                                                classes={{
                                                                    paper: styles.autocompleteDropdown
                                                                }}
                                                                renderInput={(params) => (
                                                                    <TextField
                                                                        {...params}
                                                                        className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)}
                                                                        type='text'
                                                                        placeholder='ADDRESS 2'
                                                                        onBlur={(e) => {
                                                                            e.target.value = e.target.value.trim();
                                                                            field.onBlur();
                                                                            saveUserActivity();
                                                                            saveBomHeaderDetails();
                                                                            if (isDataChanged) {
                                                                                handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, uniqueId: "shipping_details" });
                                                                                setUndoStackObject({});
                                                                                setIsDataChanged(false);
                                                                            }
                                                                        }}
                                                                        onChange={(e) => {
                                                                            setIsDataChanged(true);
                                                                        }}
                                                                        onKeyDown={(e) => {
                                                                            handleAutocompleteTabSelection(
                                                                                e,
                                                                                field,
                                                                                autocompleteOpenLine2,
                                                                                ['line2', 'line1', 'city'],
                                                                                setAutocompleteOpenLine2,
                                                                                'input[name="shipping_details.city"]'
                                                                            );
                                                                        }}
                                                                        onFocus={() => {
                                                                            setUndoStackObject({ name: "shipping_details.line2", value: watch('shipping_details.line2'), id: "shipping_details.line2", from: "header" });
                                                                        }}
                                                                    />
                                                                )}
                                                                id="shipping_details.line2"
                                                                renderOption={(props, option) => (
                                                                    <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                        <div>
                                                                            {formatAddressDisplay(option)}
                                                                        </div>
                                                                    </li>
                                                                )}
                                                                freeSolo
                                                                clearOnEscape
                                                                noOptionsText=""
                                                            />
                                                        )}
                                                    />
                                                </>
                                                <span className={styles.lastAddressFiled}>
                                                    <span className={styles.addressInputsCol1} >
                                                        <Controller
                                                            name="shipping_details.city"
                                                            control={control}
                                                            render={({ field }) => (
                                                                <Autocomplete
                                                                    disableClearable
                                                                    className={styles.autocompleteContainer}
                                                                    options={((isOrderLineChanges && initialPoData?.seller_name) ? [] : deliveryAddressData) || []}
                                                                    value={null}
                                                                    inputValue={field.value || ''}
                                                                    open={autocompleteOpenCity && (field.value?.length || 0) > 0}
                                                                    onOpen={() => {
                                                                        if ((field.value?.length || 0) > 0) {
                                                                            setAutocompleteOpenCity(true);
                                                                        }
                                                                    }}
                                                                    onClose={() => setAutocompleteOpenCity(false)}
                                                                    getOptionLabel={(option) => {
                                                                        if (typeof option === 'string') return option;
                                                                        if (!option) return '';
                                                                        return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                                    }}
                                                                    isOptionEqualToValue={(option, value) => {
                                                                        return option?.id === value?.id;
                                                                    }}
                                                                    filterOptions={(options, { inputValue }) => {
                                                                        if (!inputValue) return options;
                                                                        return options.filter(option =>
                                                                            option?.city?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                            option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                            option?.line2?.toLowerCase().includes(inputValue.toLowerCase())
                                                                        );
                                                                    }}
                                                                    classes={{
                                                                        paper: styles.autocompleteDropdown
                                                                    }}
                                                                    onInputChange={(event, newInputValue) => {
                                                                        field.onChange(newInputValue);
                                                                        setIsCreatePoDirty(true);
                                                                        // Control when dropdown opens based on input length
                                                                        if (newInputValue.length > 0) {
                                                                            setAutocompleteOpenCity(true);
                                                                        } else {
                                                                            setAutocompleteOpenCity(false);
                                                                        }
                                                                    }}
                                                                    onChange={(event, selectedOption) => {
                                                                        if (selectedOption && typeof selectedOption === 'object') {
                                                                            // Auto-populate all shipping details fields
                                                                            if (selectedOption.id !== watch('shipping_details.delivery_address_id')) {
                                                                                getDeliveryDateData(selectedOption.id)
                                                                            }
                                                                            setValue('shipping_details.delivery_address_id', selectedOption.id);
                                                                            setValue('shipping_details.line1', selectedOption.line1);
                                                                            setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                            setValue('shipping_details.city', selectedOption.city);
                                                                            setValue('shipping_details.state_id', selectedOption.state_id);
                                                                            setValue('shipping_details.zip', selectedOption.zip);
                                                                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                                            setStateDropDownValue(stateName);
                                                                            setIsCreatePoDirty(true);
                                                                            saveBomHeaderDetails();
                                                                        }
                                                                    }}
                                                                    renderInput={(params) => (
                                                                        <TextField
                                                                            {...params}
                                                                            className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)}
                                                                            type='text'
                                                                            placeholder='CITY'
                                                                            onBlur={(e) => {
                                                                                e.target.value = e.target.value.trim();
                                                                                field.onBlur();
                                                                                saveUserActivity();
                                                                                saveBomHeaderDetails();
                                                                                if (isDataChanged) {
                                                                                    handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, uniqueId: "shipping_details" });
                                                                                    setUndoStackObject({});
                                                                                    setIsDataChanged(false);
                                                                                }
                                                                            }}
                                                                            onChange={(e) => {
                                                                                setIsDataChanged(true);
                                                                            }}
                                                                            onKeyDown={(e) => {
                                                                                handleAutocompleteTabSelection(
                                                                                    e,
                                                                                    field,
                                                                                    autocompleteOpenCity,
                                                                                    ['city', 'line1', 'line2'],
                                                                                    setAutocompleteOpenCity,
                                                                                    'input[name="shipping_details.zip"]'
                                                                                );
                                                                            }}
                                                                            onFocus={() => {
                                                                                setUndoStackObject({ name: "shipping_details.city", value: watch('shipping_details.city'), id: "shipping_details.city", from: "header" });
                                                                            }}
                                                                        />
                                                                    )}
                                                                    id="shipping_details.city"
                                                                    renderOption={(props, option) => (
                                                                        <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                            <div>
                                                                                {formatAddressDisplay(option)}
                                                                            </div>
                                                                        </li>
                                                                    )}
                                                                    freeSolo
                                                                    clearOnEscape
                                                                    noOptionsText=""
                                                                />
                                                            )}
                                                        />
                                                    </span>

                                                    <div className={styles.stateZipContainer}>
                                                        <span className={clsx(styles.addressInputsCol2, errors?.shipping_details?.state_id?.message && styles.errorInput,
                                                            stateInputFocus && styles.selectShade)}>
                                                            <Tooltip
                                                                title={(isOrderLineChanges && initialPoData?.seller_name) ? 'Line level changes so cannot update state for this order' : ''}
                                                                arrow
                                                                placement={"top-end"}
                                                                disableInteractive
                                                                TransitionComponent={Fade}
                                                                TransitionProps={{ timeout: 200 }}
                                                                classes={{
                                                                    tooltip: 'stateTooltip',
                                                                }}
                                                            >
                                                                <div>

                                                                    {stateInputFocus && <>
                                                                        <div className={styles.shape1}>
                                                                            <Shape1 />
                                                                        </div>
                                                                        <div className={styles.shape2}>
                                                                            <Shape2 />
                                                                        </div>
                                                                    </>

                                                                    }
                                                                    <StateDropDown
                                                                        disabled={isOrderLineChanges && initialPoData?.seller_name}
                                                                        states={states}
                                                                        setValue={setValue}
                                                                        stateDropDownValue={stateDropDownValue}
                                                                        setStateDropDownValue={setStateDropDownValue}
                                                                        setStateInputFocus={setStateInputFocus}
                                                                        stateInputFocus={stateInputFocus}
                                                                        setIsDataChanged={setIsDataChanged}
                                                                        orderState={stateInputFocus ? watch('shipping_details.state_id') : ''}
                                                                        onChange={(e) => {
                                                                            if (!isOrderLineChanges && initialPoData?.seller_name) {
                                                                                setIsStateZipValChange(true);
                                                                            }
                                                                        }}
                                                                        onFocus={() => {
                                                                            setUndoStackObject({
                                                                                id: "shipping_details.state_id", from: "header", multipleData: {
                                                                                    ['shipping_details.state_id']: watch('shipping_details.state_id'),
                                                                                    ['shipping_details.state_code']: watch('shipping_details.state_code'),
                                                                                }
                                                                            });
                                                                        }}
                                                                        onBlur={() => {
                                                                            if (isDataChanged) {
                                                                                handleStoreUndoStack({
                                                                                    ...undoStackObject, currentMultipleData: {
                                                                                        ['shipping_details.state_id']: watch('shipping_details.state_id'),
                                                                                        ['shipping_details.state_code']: watch('shipping_details.state_code'),
                                                                                    },
                                                                                    uniqueId: "shipping_details"
                                                                                });
                                                                                setUndoStackObject({});
                                                                                setIsDataChanged(false);
                                                                            }
                                                                        }}
                                                                    />
                                                                </div>
                                                            </Tooltip>
                                                        </span>
                                                        <span className={styles.addressInputsCol3}>
                                                            <Tooltip
                                                                title={errors?.shipping_details?.zip?.message || ( (isOrderLineChanges && initialPoData?.seller_name) ? 'Line level changes so cannot update zip code for this order' : '')}
                                                                arrow
                                                                placement={"top-end"}
                                                                disableInteractive
                                                                TransitionComponent={Fade}
                                                                TransitionProps={{ timeout: 200 }}
                                                                classes={{
                                                                    tooltip: 'stateTooltip',
                                                                }}
                                                            >
                                                                <div>
                                                                    <InputWrapper>
                                                                        <CustomTextField className={clsx(styles.addressInputs, errors?.shipping_details?.zip?.message && styles.errorInput)} type='text'
                                                                            register={register("shipping_details.zip")}
                                                                            placeholder='ZIP'
                                                                            onChange={(e) => {
                                                                                register("shipping_details.zip").onChange(e);
                                                                                const zipCode = e.target.value.replace(/\D/g, '');
                                                                                setValue("shipping_details.zip", zipCode);
                                                                                setIsDataChanged(true);
                                                                                if(!isOrderLineChanges && initialPoData?.seller_name) {
                                                                                    setIsStateZipValChange(true);
                                                                                }
                                                                            }}
                                                                            maxLength={5}
                                                                            onBlur={(e) => {
                                                                                e.target.value = e.target.value.trim();
                                                                                register("shipping_details.zip").onBlur(e);
                                                                                saveUserActivity();
                                                                                saveBomHeaderDetails();
                                                                                if (isDataChanged) {
                                                                                    handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, uniqueId: "shipping_details" });
                                                                                    setUndoStackObject({});
                                                                                    setIsDataChanged(false);
                                                                                }
                                                                            }}
                                                                            onKeyDown={(e) => {
                                                                                if (e.key === 'Tab') {
                                                                                    if (!e.shiftKey) {
                                                                                        e.stopPropagation();
                                                                                        e.preventDefault();
                                                                                        const nextElement = document.querySelector('[tabindex="18"]');
                                                                                        if (nextElement instanceof HTMLElement) {
                                                                                            nextElement.focus();
                                                                                        }
                                                                                        handleDeliveryInfoContainerClickAway()
                                                                                    }
                                                                                }
                                                                            }}
                                                                            errorInput={errors?.shipping_details?.zip?.message}
                                                                            id="shipping_details.zip"
                                                                            onFocus={() => {
                                                                                setUndoStackObject({ name: "shipping_details.zip", value: watch('shipping_details.zip'), id: "shipping_details.zip", from: "header" });
                                                                            }}
                                                                            disabled={isOrderLineChanges && initialPoData?.seller_name}
                                                                        />
                                                                    </InputWrapper>
                                                                </div>
                                                            </Tooltip>
                                                        </span>
                                                    </div>

                                                </span>
                                            </span>
                                            :
                                            <span className={clsx(styles.deliverToLabel, styles.pOInputValue)}>
                                                {
                                                    (watch('shipping_details.line1') || watch('shipping_details.line2') || (watch('shipping_details.city') || stateDropDownValue || watch('shipping_details.zip'))) ? (<>
                                                        <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('shipping_details.line1') ? `${watch('shipping_details.line1')}` : ''}</p>
                                                        <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('shipping_details.line2') ? `${watch('shipping_details.line2')}` : ''}</p>
                                                        <span className={styles.lastAddressFiled1}>
                                                            <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('shipping_details.city') ? `${watch('shipping_details.city')}` : ''}</p>
                                                            <span className={styles.stateZipContainer1}>
                                                                <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{stateDropDownValue ? stateDropDownValue : ''}</p>
                                                                 <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('shipping_details.zip') ? `${watch('shipping_details.zip')}` : ''}</p>
                                                            </span>
                                                        </span>
                                                    </>) : (<span>DELIVER TO</span>)
                                                }

                                            </span>
                                        }

                                    </div>
                                </ClickAwayListener>
                            ) : ( 
                                <div className={styles.pOInputValue}>
                                    <span>{watch('shipping_details.line1') ? `${watch('shipping_details.line1')}` : ''} &nbsp; {watch('shipping_details.line2') ? `${watch('shipping_details.line2')}` : ''}</span>
                                    <span>{watch('shipping_details.city') ? `${watch('shipping_details.city')}` : ''}, {stateDropDownValue ? stateDropDownValue : ''} {watch('shipping_details.zip') ? `${watch('shipping_details.zip')}` : ''}</span>
                                </div>
                            )}
                        </div>

                    </div>
                </div>
                <div className={styles.rightGridHeader}>
                    <div className={styles.poCreateDateContainer}
                    >
                        <span className={styles.uploadLabel1}>
                            Fulfilled By:
                        </span>
                        <span className={styles.uploadLabel2}>
                            {initialPoData?.seller_name || '-'}
                        </span>
                        <span className={styles.uploadLabel3}>
                            {initialPoData?.seller_company_name}
                        </span>
                    </div>
                </div>
                <div className={styles.rightGridHeader}>
                    <div className={styles.poCreateDateContainer}
                    >
                        <span className={styles.uploadLabel1}>
                            PO Create Date:
                        </span>
                        <span className={styles.uploadLabel2}>
                            {initialPoData?.created_date ?  formatDisplayDateForTemplate(convertUtcToLocalTime(initialPoData?.created_date) , "ddd, MMM DD, YYYY"): '-'}
                        </span>
                        <span className={styles.uploadLabel3}>
                            {initialPoData?.created_date ? formatDisplayDateForTemplate(convertUtcToLocalTime(initialPoData?.created_date) , "hh:mmA ") : '-'}
                        </span>
                    </div>
                </div>

            </div>
            {initialPoData?.order_level_dispute?.deliver_by &&
                (
                    <div className={clsx(styles.deliveryDateContainer, !watch('isEdit') && styles.isEditMode)}>
                        Delivery By:
                        <div className={styles.disputeHeaderInfoGrid}>
                        <div className={styles.leftGridHeader}>
                            <div className={styles.col1}>
                                <div className={styles.pOInputValue}>
                                    {initialPoData?.order_level_dispute?.deliver_by.map((item: any, i: number) => {
                                        const isMe = userData?.data?.type === item?.created_by;
                                        const isBuyer = item?.created_by === userRole.buyerUser;
                                        const counterLabel = item.counter_status === disputeCounterStatus.original ? "Originally" : isMe ? "Me " : isBuyer ? "Buyer" : "Supplier";
                                        return (
                                            <div key={item.counter_id + i} className={styles.disputeItem}>
                                                <label htmlFor={item.counter_id}>{counterLabel}</label>
                                                <div className={clsx(item.counter_status !== disputeCounterStatus.pending && styles.strikeThroughText)}>
                                                    {formatDisplayDateForTemplate(item.delivery_date, "ddd, MMM DD, YYYY")}
                                                </div>
                                            </div>
                                        )
                                    })}
                                </div>

                            </div>
                        </div>
                        <div className={styles.rightGridHeader}>
                        <div className={styles.actionButtonsContainer}>
                            <button className={styles.actionButtons}  onClick={handleAcceptCounter}>Accept</button>
                            <button className={styles.actionButtons} onClick={handleCounterClick}>Counter</button>
                            <button className={styles.actionButtons} onClick={handleRejectCounter}>Reject</button>
                            </div>
                        </div>
                        {isCalendarOpen && (
                            <ClickAwayListener onClickAway={handleClickAway}>
                                <div className={styles.calendarWrapper}>
                                    <Calendar
                                        // value={new Date()}
                                        setValue={(field: string, value: string) => {
                                            if (field === 'delivery_date') {
                                                console.log('value', value);
                                                // setValue(`cart_items.${index}.line_dispute_counter.${watch(`cart_items.${index}.line_dispute_counter`)?.length - 1}.delivery_date`, value);
                                            }
                                        }}
                                        isCalendarOpen={isCalendarOpen}
                                        setIsCalendarOpen={setIsCalendarOpen}
                                        disableDeliveryDate={false}
                                        handleOpenCalendar={handleOpenCalendar}
                                        saveUserActivity={() => { }}
                                        saveBomHeaderDetails={() => { }}
                                        onDateSelect={handleDateSelect}
                                        allowedDates={[]}
                                    />
                                </div>
                            </ClickAwayListener>
                        )}
                        </div>
                        {
                    currentFocusedItem?.id === 'deliver_by' && (
                        <span className={styles.pointRight}>
                            <PointRightIcon />
                        </span>
                    )   
                }
                    </div>
                )
            }
            <Dialog
                open={addressDialogOpen}
                onClose={(event) => setAddressDialogOpen(false)}
                transitionDuration={100}
                disableScrollLock={true}
                // container={HeaderDetailsConfirmedRef.current}

                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid transparent',
                    borderRadius: '0px 0px 20px 20px',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0,
                        width: '100%',
                        maxWidth: '735px',
                        borderRadius: '16px',
                        boxShadow: '0 0 67.4px 4px #000',
                        backgroundColor: '#222329',
                    }
                }}
                hideBackdrop
                classes={{
                    root: styles.customeAddressPopup,
                    paper: styles.dialogContent
                }}
            >
                <ShipmentsTab isCreate={true} closeDialog={() => { setAddressDialogOpen(false); navigate(routes.homePage) }} onSuccess={handleOnSuccessShippingDetails} />
            </Dialog>
        </div>
    )
})

export default OrderManagementHeaderInfo;