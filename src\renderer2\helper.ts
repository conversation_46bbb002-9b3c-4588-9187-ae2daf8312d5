
import axios from "axios";
import { saveAs } from 'file-saver';
import rg4js from "raygun4js";
import { commomKeys, localStorageKeys, routes } from "./common";
import { Auth } from 'aws-amplify';
import { ReferenceDataProduct } from "./types/ReferenceDataProduct";
import { getQuantityGroup, getRegionByZip } from "./pages/search/searchUIUtils";
import { ProductPricingModel } from "./types/Search";
import { dollarSign, fileType, neutralPricingPrefix, newPricingPrefix, noIdGeneric, orderIncrementPrefix, orderType, priceUnits, reactQueryKeys, referenceProductItem, referenceProductQuantity, useBuyerSettingStore, useCreatePoStore, useGlobalStore, useOrderManagementStore, userRole, useSearchStore, useSellerOrderStore } from "@bryzos/giss-ui-library";
import { v4 as uuidv4 } from 'uuid';
import { getPriceExample } from "./utility/priceIntegratorExample";
import dayjs from "dayjs";
import { useLeftPanelStore } from "./component/LeftPanel/LeftPanelStore";
import { createExcelBlob, ExcelBuildOptions } from "./pages/exporttToExcelUtils";
import useDialogStore from "./component/DialogPopup/DialogStore";
import dayjsFactory from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useLocation, useNavigate } from "react-router-dom";
import { useBomPdfExtractorStore } from "./pages/buyer/BomPdfExtractor/BomPdfExtractorStore";

export const VERSION_NUMBER = "*******";
export const commonRaygunError = "common-raygun-error";
export const trueVaultRaygunError = "trueVault-raygun-error";
export const unhandledError = "unhandled-error";
export const pdfGenreationError = "pdf-generation-error";

export const SEND_INVOICES_TO = "<EMAIL>";
export const SHIPPING_DOCS_TO = "<EMAIL>";
export const MENU_ANIMATION_DURATION = 700;
export const mainConfig = {
  log: {
    shareLogSize: 0
  } 
};

let channelWindow = {}
let navigate = null;
let userAppData = null;
export const setChannelWindow = (channelWindowList: {}) => {
  channelWindow = channelWindowList
}

export const getChannelWindow = () => {
  return channelWindow;
}

export const setNavigate = (_navigate) => {
  if(navigate === null){
    navigate = _navigate;
  }
}

export const navigatePage = (currentPage, nextPage) => {
  if(currentPage === routes.orderConfirmationPageSeller 
    || currentPage === routes.acceptOrderPage
    || currentPage === routes.orderConfirmationPage
    ){
    navigate(nextPage.path,{ ...nextPage.state ,replace: true})
  }else{
    navigate(nextPage.path, nextPage.state)
  }
}
 
// Format a number as a currency value with a dollar sign
export function formatCurrency(number) {
  return Number(number).toLocaleString('en-US', {
    currency: 'USD',
    style: 'currency',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).replace("$", "$ ");
}

// Format a number with commas without dollar sign
export function formatNumber(number: number) {
  return number.toLocaleString('en-US');
}

export const formatToTwoDecimalPlaces = (
  value?: string
) => {
  if (value && +value) {
   return Number(value).toLocaleString('en-US',{
    style:'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
   });
  } else {
    return '0.00';
  }
};

export const formatPhoneNumberRemovingCountryCode = (phoneNo: string)=>{
  if(phoneNo)
  return formatPhoneNumber( phoneNo.replace(commomKeys.countryCode,'') );
  return phoneNo;
}

export const formatPhoneNumber = (phoneNo: string) => {
  const phoneNumber = phoneNo.replace(/\D/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) {
    return phoneNumber;
  } else if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  } else {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
};

export const formatPhoneNumberWithCountryCode = (phoneNo: string) => {
    return commomKeys.countryCode + phoneNo.replace(/[^\w\s]/gi, '').replace(/\s+/g, '');
};

export const addWorkingDaysToDate = (days: number) => {
  let date = new Date();
  let count = 0;

  while (count < days) {
    date.setDate(date.getDate() + 1);
    if (date.getDay() !== 0 && date.getDay() !== 6) {
      count++;
    }
  }
  return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear().toString().slice(2)}`;
};

export const formatEIN = (value: string) => {
  const ein = value.replace(/\D/g, '');
  const einLength = ein.length;
  if (einLength < 3) {
    return ein;
  } else {
    const firstPart = ein.slice(0, 2);
    const secondPart = ein.slice(2, 9);
    return `${firstPart}-${secondPart}`;
  }
};


export async function downloadFilesUsingFetch(url: RequestInfo | URL, fileName: string | undefined, type: any) {
  try {
    const response = await fetch(url);
    const responseData = await response.blob();

    if(responseData.type === 'application/json'){
      return false;
    }
    const blob = new Blob([responseData], { type: type });
    saveAs(blob, fileName);
    return true;
  } catch (error) {
    console.error('Error downloading the file:', error);
  }
}

export async function downloadFiles(url: string, fileName: string | undefined, type: any) {
  try {
    const response = await axios.get(url,{
      responseType: 'blob'
    });

    if(response.data.type === 'application/json'){
      return false;
    }
    const blob = new Blob([response.data], { type: type });
    saveAs(blob, fileName);
    return true;
  } catch (error) {
    console.error('Error downloading the file:', error);
  }
}

export const downloadFileWithAnyExtension = async (url: string) => {
  let index = url.length-1;
  for(  ;index >= 0 ;index-- ){
  if(url.charAt(index)==='/'){
      break;
  }
  }
  let fileName = url.substring(index+1, url.length);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

export const dispatchRaygunError = (error: unknown, tags: string | string[]) => {
  rg4js('send', {
    error: error,
    tags: tags
  })
}

export const formatCurrencyWithComma = (value: string) => {
  if (value) {
    return value.replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
  } else {
    return "";
  }
};

export const removeCommaFromCurrency = (value: string) => {
  if (value) {
    return value.replace(/\,/g, "");
  } else {
    return "";
  }
};

export const getFloatRemainder = (dividend: string | number | null, divisor: number) => {
  if (dividend && +divisor) {
    dividend = +dividend;
    divisor = +divisor;

    var dividendDecimalCount = (dividend.toString().split('.')[1] ?? '').length;
    var divisorDecimalCount = (divisor.toString().split('.')[1] ?? '').length;
    var decimalCount = dividendDecimalCount > divisorDecimalCount ? dividendDecimalCount : divisorDecimalCount;

    var dividendInt = parseInt(dividend.toFixed(decimalCount).replace('.', ''));
    var divisorInt = parseInt(divisor.toFixed(decimalCount).replace('.', ''));

    return (dividendInt % divisorInt) / Math.pow(10, decimalCount);
  } else {
    throw new Error("dividend or divisor value is invalid");
  }
}

export const get2DigitFormater = () => {
  return  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});
}

export const get4DigitFormater = () => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 4,
    maximumFractionDigits: 4,
});
}

export const format4DigitAmount = (value: string | number) => {
  const decimal4Formater = get4DigitFormater();
  const _value = +value ? +value : 0;
  return decimal4Formater.format(_value).replace("$", "");
};

export const format2DigitAmount = (value: string | number) => {
  const decimal2Formater = get2DigitFormater();
  const _value = +value ? +value : 0;
  return decimal2Formater.format(_value).replace("$", "");
};

export function getNumericVersion(version: string | undefined)
{
  if (version && typeof version === "string") {
    return Number(version.split(".").join(""));
  }
  return false; 
}

export async function getAccessToken() {
  const user = await Auth.currentSession();
  const accessToken = user.getAccessToken();
  const jwt = accessToken.getJwtToken();
  return jwt;
}

export const getProductMapping = (productList: ReferenceDataProduct[], userData) => {
  const productMap = {};
  productList.forEach((product) => {
    // product.Buyer_Pricing_CWT = product.Actual_Buyer_Pricing_CWT;
    // product.Buyer_Pricing_Ea = product.Actual_Buyer_Pricing_Ea;
    // product.Buyer_Pricing_Ft = product.Actual_Buyer_Pricing_Ft;
    // product.Buyer_Pricing_LB = product.Actual_Buyer_Pricing_LB;
    // product.Buyer_Pricing_Net_Ton = product.Actual_Buyer_Pricing_Net_Ton;

    // product.Seller_Pricing_CWT = product.Actual_Seller_Pricing_CWT;
    // product.Seller_Pricing_Ea = product.Actual_Seller_Pricing_Ea;
    // product.Seller_Pricing_Ft = product.Actual_Seller_Pricing_Ft;
    // product.Seller_Pricing_LB = product.Actual_Seller_Pricing_LB;
    // product.Seller_Pricing_Net_Ton = product.Actual_Seller_Pricing_Net_Ton;

    // product.Neutral_Pricing_CWT = product.Actual_Neutral_Pricing_CWT;
    // product.Neutral_Pricing_Ea = product.Actual_Neutral_Pricing_Ea;
    // product.Neutral_Pricing_Ft = product.Actual_Neutral_Pricing_Ft;
    // product.Neutral_Pricing_LB = product.Actual_Neutral_Pricing_LB;
    // product.Neutral_Pricing_Net_Ton = product.Actual_Neutral_Pricing_Net_Ton;

    // const spreadRate = !product.is_safe_product_code ? userData.disc_discount_rate : 1;
    // if (userData.disc_is_discounted) {
    //   product.Buyer_Pricing_CWT = "$" + (product[userData.disc_discount_pricing_column + '_CWT'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Ea = "$" + (product[userData.disc_discount_pricing_column + '_Ea'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Ft = "$" + (product[userData.disc_discount_pricing_column + '_Ft'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_LB = "$" + (product[userData.disc_discount_pricing_column + '_LB'].trim().replace(/[\$,]/g, '') * spreadRate);
    //   product.Buyer_Pricing_Net_Ton = "$" + (product[userData.disc_discount_pricing_column + '_Net_Ton'].trim().replace(/[\$,]/g, '') * spreadRate);

    // }

    Object.keys(product).forEach(key => {
      if(key.toLowerCase().includes(neutralPricingPrefix)){
        let newKey = key.toLowerCase();
        if(key.toLowerCase().includes(priceUnits.ea)){
          newKey = neutralPricingPrefix + priceUnits.pc;
        }
        product[newKey] = product[key];
      }
      if (key.toLowerCase().includes(orderIncrementPrefix)) {
        let newKey = key.toLowerCase();
        if (key.toLowerCase().includes(priceUnits.ea)) {
          newKey = orderIncrementPrefix + priceUnits.pc;
        }
        product[newKey] = product[key];
        if (newKey !== key) delete product[key];
      }
    });
    product.QUM_Dropdown_Options = product.QUM_Dropdown_Options?.split(",").map((item: any) => item.trim().toLowerCase()).join(",").replace(priceUnits.ea, priceUnits.pc);
    product.PUM_Dropdown_Options = product.PUM_Dropdown_Options?.split(",").map((item: any) => item.trim().toLowerCase()).join(",").replace(priceUnits.ea, priceUnits.pc);

    productMap[product.Product_ID] = product;
  });
  return productMap;
}

export const updatedAllProductsData = (products: ReferenceDataProduct[]) => {
  if (products?.length) {
    return products.map(product => ({
      ...product,
      Actual_Buyer_Pricing_CWT: product.Buyer_Pricing_CWT,
      Actual_Buyer_Pricing_Ea: product.Buyer_Pricing_Ea,
      Actual_Buyer_Pricing_Ft: product.Buyer_Pricing_Ft,
      Actual_Buyer_Pricing_LB: product.Buyer_Pricing_LB,
      Actual_Buyer_Pricing_Net_Ton: product.Buyer_Pricing_Net_Ton,

      Actual_Seller_Pricing_CWT: product.Seller_Pricing_CWT,
      Actual_Seller_Pricing_Ea: product.Seller_Pricing_Ea,
      Actual_Seller_Pricing_Ft: product.Seller_Pricing_Ft,
      Actual_Seller_Pricing_LB: product.Seller_Pricing_LB,
      Actual_Seller_Pricing_Net_Ton: product.Seller_Pricing_Net_Ton,

      Actual_Neutral_Pricing_CWT: product.Neutral_Pricing_CWT,
      Actual_Neutral_Pricing_Ea: product.Neutral_Pricing_Ea,
      Actual_Neutral_Pricing_Ft: product.Neutral_Pricing_Ft,
      Actual_Neutral_Pricing_LB: product.Neutral_Pricing_LB,
      Actual_Neutral_Pricing_Net_Ton: product.Neutral_Pricing_Net_Ton,
    }));
  }
  return products;
}


export const handleConsoleLog = (component: string, log: string, source) => {
  console.log({
    sourceType:'renderer',
    source,
    location: location.pathname,
    component,
}, log);
}

export const handleConsoleError = (component: string, error, source) => {
  console.error({
    sourceType:'renderer',
    source,
    location: location.pathname,
    component,
}, error);
}

export const handleConsoleWarn = (component: string, warn, source) => {
  console.warn({
    sourceType:'renderer',
    source,
    location: location.pathname,
    component,
}, error);
  // console.warn(` [renderer|${component}|${location.pathname}] `, warn);
}

export const priceFormatter = ( product: ProductPricingModel ) => {
  const { userData }: any = useGlobalStore.getState();
  const { selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
    const decimal2Formater = get2DigitFormater();
    const decimal4Formater = get4DigitFormater();
    const quantityGroup = getQuantityGroup(orderSizeSliderValue);
    let price = +(String(removeCommaFromCurrency((product[`${selectedPriceUnit}_price`])?.replace(dollarSign, ""))) ?? 0);
    const qttyMultiplier = userData.multipliers["" + product.id] ? userData.multipliers["" + product.id][quantityGroup] : userData.multipliers["3010"];
    const regionGroup = getRegionByZip(Number(searchZipCode));
    const regionMultiplier = userData.multipliers["" + product.id] ? userData.multipliers["" + product.id][regionGroup] : userData.multipliers["10010"][regionGroup];
    if (selectedPriceUnit === referenceProductQuantity.lb && !product.is_safe_product_code) {
        return decimal4Formater.format(price ? price * qttyMultiplier * regionMultiplier : 0).replace(dollarSign, "");
    } else {
        return decimal2Formater.format(price ? price * qttyMultiplier * regionMultiplier : 0).replace(dollarSign, "");
    }
};


export const newPriceFormatter = (product: ProductPricingModel) => {
  const productId = product.id;
  const { buyerSetting } = useBuyerSettingStore.getState();
  const decimal2Formater = get2DigitFormater();
  const decimal4Formater = get4DigitFormater();
  const { selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
  if (selectedPriceUnit === referenceProductQuantity.lb && !product.is_safe_product_code) {
    return decimal4Formater.format(product[`${selectedPriceUnit}_price`] ? product[`${selectedPriceUnit}_price`] : 0).replace(dollarSign, "");
  } else {
    return decimal2Formater.format(product[`${selectedPriceUnit}_price`] ? product[`${selectedPriceUnit}_price`] : 0).replace(dollarSign, "");
  }
}


export const  getUserAppData = () => userAppData
export const  setUserAppData = (data) => { 
  userAppData = data 
}

export const stringifyError = (err) => {
  let lastTwoTraces
  let error = err;
  if (err instanceof Error) {
    if (err.stack) {
      const stackLines = err.stack.split("\n");
      lastTwoTraces = stackLines.slice(0, 3).join("\n"); // First line is error message, next two are traces
    }
    error = {
      name: err.name,
      message: err.message,
      stack: lastTwoTraces
    };
  }

  return error
}

export const removeAutoCredential = (channelWindow) => {
  try{
    if(channelWindow?.handleLogout){
      window.electron.send({ channel: channelWindow.handleLogout });
    }
  document.cookie.split(";").forEach((c) => {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
    });
  }catch(err){
    console.error(err);
  }
}

export const clearLocal = (key: string) => {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    console.warn('Could not clear value from localStorage', e);
  }
}

export function setLocal(key: string, value: any) {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (e) {
    console.warn('Could not store value in localStorage', e);
  }
}

export function getLocal<T = any>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (e) {
    console.warn('Could not read value from localStorage', e);
    return defaultValue;
  }
}

export const getFormattedProductPricingForSearchSelection = (product: ReferenceDataProduct) => {
  const lineSessionId = uuidv4();
  const productWithPrice: ProductPricingModel = {
      "id": product.Product_ID,
      "UI_Description": product.UI_Description,
      "cwt_price": product.cwt_price,
      "ft_price": product.ft_price,
      "lb_price": product.lb_price,
      "pc_price": product.pc_price,
      "product_type_pipe": product?.Key2 ? product.Key2 === referenceProductItem.pipe : false,
      "line_session_id": lineSessionId,
      "is_safe_product_code": product.is_safe_product_code,
      "domestic_material_only": product.domestic_material_only,
      // [neutralPricingPrefix+ priceUnits.ea]: Number(product?.[neutralPricingPrefix+ priceUnits.ea]) || '',
      // [neutralPricingPrefix+ priceUnits.cwt]: Number(product?.[neutralPricingPrefix+ priceUnits.cwt]) || '',
      // [neutralPricingPrefix+ priceUnits.ft]: Number(product?.[neutralPricingPrefix+ priceUnits.ft]) || '',
      // [neutralPricingPrefix+ priceUnits.lb]: Number(product?.[neutralPricingPrefix+ priceUnits.lb]) || '',
  }
  Object.keys(priceUnits).forEach((key: any) => {
    if(product?.[neutralPricingPrefix+key]){
      productWithPrice[neutralPricingPrefix+ key] = Number(product?.[neutralPricingPrefix + key]) || '';
    }
  })
  return productWithPrice;
}

export const formatPhoneNumberWithHyphen = (phoneNo: string) => {
  const phoneNumber = phoneNo.replace(/\D/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) {
      return phoneNumber;
  } else if (phoneNumberLength < 7) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;
  } else {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
};

export const unformatPhoneNumber = (formattedPhoneNo: string) => {
  return `+1${formattedPhoneNo.replace(/\D/g, '')}`;
};


export const getHomeRoute = () => {
  const { userData } = useGlobalStore.getState();
  if(userData?.data?.type === "SELLER"){
    return routes.previewOrderPage;
  }else{
    return routes.homePage;
  }
}

export const fetchPrice = async (productData: any, zipCode: string, orderSize: number) => {
    try{
        const productMapping = useGlobalStore.getState().productMapping;
        const setShortListedSearchProductsData = useSearchStore.getState().setShortListedSearchProductsData;
        const shortListedSearchProductsData = useSearchStore.getState().shortListedSearchProductsData;
        const orderSizeSliderValue = useSearchStore.getState().orderSizeSliderValue;
        const buyerSetting = useBuyerSettingStore.getState().buyerSetting;
        const selectedDomesticOption = useSearchStore.getState().selectedDomesticOption;
        const defaultZipCode = buyerSetting?.price_search_zip || '63105';
        const _zipcode = zipCode ? zipCode : defaultZipCode;
        let productList: any = [];
        if (productData?.length > 0) {
            productData?.forEach((product: any) => {
                const productId = product.product_id || (product?.Product_ID ? product?.Product_ID : product.id);
                const productDataObj = { ...productMapping[productId] };
                productList.push({ ...product, ...productDataObj });
            })
        }
        let productIdList = productList.filter((product: any) => !product.is_safe_product_code).map((product: any) => product.product_id || (product?.Product_ID ? product?.Product_ID : product.id));
        if (productIdList.length === 0 && !Array.isArray(productData)) productIdList = productData.Product_ID;
        const newPrices = String(productIdList).length ? await getPriceExample(productIdList, _zipcode, Math.floor(orderSize || orderSizeSliderValue)) : {};
        if(Array.isArray(productData)){
            productList = productList.map((product: any) => {
              const productId = product.product_id || (product?.Product_ID ? product?.Product_ID : product.id);
              Object.keys(priceUnits).forEach((key: any) => {
                  (newPrices?.[productId]?.[key] || product?.[neutralPricingPrefix + key]) ?
                      (product[`${key}_price`] = (product.is_safe_product_code) ? product?.[neutralPricingPrefix + key] : newPrices[productId]?.[key])
                      : (product[`${key}_price`] = 0);
              })
                if(Array.isArray(productData) && product?.price) product[`${productData[0].price_unit}_price`] = parseFloat(product.price.replace(/[$,]/g, ""));
                const productWithPrice = getFormattedProductPricingForSearchSelection(product);
                return productWithPrice;
            })
        }else{
            const spreadProduct = {...productData};
            Object.keys(priceUnits).forEach((key: any) => {
                (newPrices?.[key] || spreadProduct?.[neutralPricingPrefix + key]) ?
                    (spreadProduct[`${key}_price`] = (spreadProduct.is_safe_product_code) ? spreadProduct?.[neutralPricingPrefix + key] : newPrices[key]) :
                    (spreadProduct[`${key}_price`] = 0);
            })
            const productWithPrice = getFormattedProductPricingForSearchSelection(spreadProduct);
            productList = [productWithPrice, ...shortListedSearchProductsData];
        }
        setShortListedSearchProductsData(productList);
        if (selectedDomesticOption) {
            productList = productList.filter(product => product.domestic_material_only);
        }
        return productList;
    }catch(error){
      console.log("check error @>>>>>>>", error);
        return [];
    }
}

export const updateSelectedPriceSearchData = (searchProductsData: ProductPricingModel[]) => {
    const selectedSavedSearch = useSearchStore.getState().selectedSavedSearch;
    const savedSearchProducts = useSearchStore.getState().savedSearchProducts;
    const setSelectedSavedSearch = useSearchStore.getState().setSelectedSavedSearch;
    const setSavedSearchProducts = useSearchStore.getState().setSavedSearchProducts;
    const { selectedPriceUnit, searchZipCode, orderSizeSliderValue } = useSearchStore.getState();
    const savedSearchData = getLocal(localStorageKeys.instantPriceSearch, null) || selectedSavedSearch;

    if(savedSearchData?.id){
      const updatedSelectedPriceSearchData = {
          ...savedSearchData,
          products: searchProductsData.map((product: ProductPricingModel) => {
              return {
                  product_id: product.id,
                  product_description: product.UI_Description,
                  price: newPriceFormatter(product),
                  price_unit: selectedPriceUnit.toLowerCase(),
                  domestic_material_only: product.domestic_material_only
              }
          }),
          item_count: searchProductsData.length,
          zipcode: searchZipCode,
          order_size: String(orderSizeSliderValue),
          time_stamp: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      setSelectedSavedSearch(updatedSelectedPriceSearchData);
      setLocal(localStorageKeys.instantPriceSearch, updatedSelectedPriceSearchData);
      const updatedSavedSearchProducts = savedSearchProducts.map((savedSearchProduct: any)=>{
          if(savedSearchProduct?.id === savedSearchData?.id){
              return updatedSelectedPriceSearchData;
          }
          return savedSearchProduct;
      });
      setSavedSearchProducts(updatedSavedSearchProducts);
    }
}

export const getOrderSizeData = (bracketList: any[], orderSize: number) => {
    return bracketList.find((bracket: any, index: number) => {
        // For last bracket, only check min since it's unlimited max
        if (index === bracketList.length - 1) {
            return orderSize >= Number(bracket.min_weight);
        }
        // For other brackets check if order size falls between min and max
        return orderSize >= Number(bracket.min_weight) && orderSize < Number(bracketList[index + 1].min_weight);
    });
}

export const formatOrderSizeToDisplayText = (bracketList: any[], orderSize: number) => {
  const orderSizeData = getOrderSizeData(bracketList, orderSize);
  const formattedText = orderSizeData ? `Based Upon ${Number(orderSizeData?.min_weight) === Number(bracketList[bracketList.length - 1].min_weight) ? Number(orderSizeData?.min_weight).toLocaleString() + '+' : `${Number(orderSizeData?.min_weight).toLocaleString()} to ${Number(orderSizeData?.max_weight).toLocaleString()}`} LBS` : '-';
  return formattedText;
}
export const exportToExcel = async (data: RowData[], filename: string, options: ExcelBuildOptions = {}) => {
  const blob = await createExcelBlob(data, options);
  saveAs(blob, `${sanitizeFilename(filename)}.xlsx`);
}

//it removes the mime data from the base64 string
export async function urlToBase64NoPrefix(url: string): Promise<string> {
  const res = await fetch(url);
  const blob = await res.blob();
  const dataUrl = await new Promise<string>((resolve, reject) => {
    const fr = new FileReader();
    fr.onload = () => resolve(String(fr.result));
    fr.onerror = reject;
    fr.readAsDataURL(blob);
  });
  return dataUrl.split(',')[1]; // remove "data:image/...;base64,"
}

export function sanitizeFilename(name:string) {
    // Replace disallowed characters: \ / : * ? " < > | and control characters (ASCII < 32)
    const illegalCharsRegex = /[\\\/:\*\?"<>\|\x00-\x1F]/g;

    // Replace disallowed Unicode characters for macOS (e.g., colon)
    const macUnsafe = /[:]/g;

    // Combine and replace with empty string, then replace spaces with underscores
    return name
        .replace(illegalCharsRegex, '')
        .replace(macUnsafe, '')
        .replace(/\s+/g, '_')
        .substring(0, 255); // Truncate to 255 characters (safe cross-platform length)
}

 export const handleSaveSearchProducts = async (priceSearchData: any, saveSearchProductsMutation: any) => {
   if (getLocal(localStorageKeys.isSaving, false)) {
     return;
   }
   setLocal(localStorageKeys.isSaving, true);
  const setSelectedProductsData = useSearchStore.getState().setSelectedProductsData;
  const setSaveFeedbackMap = useSearchStore.getState().setSaveFeedbackMap;
  const setFocusSingleProduct = useSearchStore.getState().setFocusSingleProduct;
   try{
      const payload = {
          data: {
              id: priceSearchData?.id.includes(noIdGeneric) ? undefined : priceSearchData?.id,
              title: priceSearchData?.title,
              zipcode: priceSearchData?.zipcode.trim(),
              order_size: String(priceSearchData?.order_size),
              source: "search",
              products: priceSearchData?.products
          }
      }
      const response = await saveSearchProductsMutation(payload as any);
 
      if (response?.data?.data) {
        console.log("Pricing saved successfully");
        setSelectedProductsData([]);
        setSaveFeedbackMap({});
        setFocusSingleProduct({});
        clearLocal(localStorageKeys.instantPriceSearch);
        return response?.data?.data;
      } else {
        console.error("Failed to save pricing");
      }
   } catch(error){
      console.error('Error saving pricing:', error);
   }finally{
    setLocal(localStorageKeys.isSaving, false);
   }
  }

  export const convertDateFormat = (input: string) => {
    const targetFormat = 'YYYY-MM-DD HH:mm:ss'
  
    if (!input) return 'Invalid date'
  
    // Check if input is already in correct format
    if (dayjs(input, targetFormat, true).isValid()) {
      return input
    }
  
    // Remove "@" and timezone abbreviations (2–4 uppercase letters at the end)
    const cleanedInput = input
      .replace('@', '')
      .replace(/\s+\b[A-Z]{2,4}\b$/, '') // <- strip CT, EST, etc.
      .trim()
  
    const parsedDate = dayjs(cleanedInput, 'M/D/YYYY h:mma')
  
    if (!parsedDate.isValid()) {
      return 'Invalid date'
    }
  
    return parsedDate.format(targetFormat)
  }

  export const calculateTotalPurchase = (item: any): number => {
    if (item?.total_purchase) {
      return parseFloat(item.total_purchase) || 0;
    }
  
    const buyerPoPrice = parseFloat(item?.buyer_po_price) || 0;
    const salesTax = parseFloat(item?.sales_tax) || 0;
    const processingFees = parseFloat(item?.processing_fees) || 0;
    const depositAmount = parseFloat(item?.deposit_amount) || 0;
    const subscriptionAmount = parseFloat(item?.subscription) || 0;
  
    return buyerPoPrice + salesTax + processingFees + depositAmount + subscriptionAmount;
  };


type MatchType = 'contains' | 'startsWith' | 'endsWith' | 'exactMatch';

interface FilterResult<T> {
  filteredObj: T;
  field: string;
}

export function filterObjectsByString<T extends object>(
  data: T[],
  searchString: string,
  matchType: MatchType = 'contains',
  dateKey: string = 'created_date'
): FilterResult<T>[] {
  const lowerSearch = searchString.toLowerCase();

  return data.flatMap((obj, index) => {
    for (const [key, value] of Object.entries(obj)) {
      if (key === 'id') continue;
      const val = String(value).toLowerCase();
      const isMatch =
        (matchType === 'contains' && val.includes(lowerSearch)) ||
        (matchType === 'startsWith' && val.startsWith(lowerSearch)) ||
        (matchType === 'endsWith' && val.endsWith(lowerSearch)) ||
        (matchType === 'exactMatch' && val === lowerSearch);

      if (isMatch) {
        //return [{ filteredObj: obj, field: key, index }];
        return [{ id: index.toString(), label: obj[key], field: key, index, date: formatDateForGlobalSearch(obj[dateKey].split(' ')[0].trim()) }];
      }
    }
    return [];
  });
}


export const formatDateForGlobalSearch = (dateStr: string) => {
  const date = new Date(dateStr);
  
  const weekday = date.toLocaleDateString('en-US', { weekday: 'short' });
  const month = date.getMonth() + 1;
  const day = date.getDate();
  
  const formatted = `${weekday} ${month}/${day}`;
  return formatted;
}

export function flattenObject(
  obj: any,
  prefix = '',
  ignorePatterns: RegExp[] = []
): Record<string, any> {
  let result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    const path = prefix ? `${prefix}.${key}` : key;

    // Check if this path matches any ignore pattern
    if (ignorePatterns.some((regex) => regex.test(path))) {
      continue; // skip this key entirely
    }

    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        const arrayKey = `${path}#${index}`;

        // Skip if arrayKey matches any ignore pattern
        if (ignorePatterns.some((regex) => regex.test(arrayKey))) {
          return; // skip this array element
        }

        if (item !== null && typeof item === 'object') {
          Object.assign(result, flattenObject(item, arrayKey, ignorePatterns));
        } else {
          result[arrayKey] = item;
        }
      });
    } else if (value !== null && typeof value === 'object') {
      Object.assign(result, flattenObject(value, path, ignorePatterns));
    } else {
      result[path] = value;
    }
  }

  return result;
}


export function transformOrderToSheetData(obj) {
  const formatDate = (rawDate) => {
      const date = new Date(rawDate);
      return date.toLocaleString('en-US', {
      weekday: undefined,
      year: 'numeric',
      month: 'long',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      });
  };

  const baseHeaderStyle = {
      b: true,
      backgroundColor: 'FFFFEB9C',
  };

  const baseRowStyle = {
      border: true,
  };

  const result = [];

  // Title
  result.push({
      row: [
      { cellText: 'Title', style: baseHeaderStyle },
      { cellText: obj.buyer_internal_po }
      ],
      style: baseRowStyle,
  });

  // Delivery Date
  result.push({
      row: [
      { cellText: 'Delivery Date', style: baseHeaderStyle },
      { cellText: formatDate(obj.delivery_date) }
      ],
      style: baseRowStyle,
  });

  // Address
  result.push({
      row: [{ cellText: 'Address', style: baseHeaderStyle }],
      style: baseRowStyle,
  });

  ['line1', 'line2', 'city', 'zip'].forEach((field) => {
      result.push({
      row: [
          { cellText: field.charAt(0).toUpperCase() + field.slice(1), style: baseHeaderStyle },
          { cellText: obj.shipping_details[field] }
      ],
      style: baseRowStyle,
      });
  });

  // State
  result.push({
      row: [
      { cellText: 'State', style: baseHeaderStyle },
      { cellText: obj.shipping_details.state_code }
      ],
      style: baseRowStyle,
  });

  // Empty row
  result.push({ row: [{ cellText: ' ' }] });

  // Table Header
  result.push({
      row: [
      { cellText: 'Line #' },
      { cellText: 'Domestic Material' },
      { cellText: 'Description' },
      { cellText: 'Quantity' },
      { cellText: 'Quantity Unit' },
      { cellText: 'Extended Price' },
      ],
      style: {
      b: true,
      backgroundColor: 'FFFFEB9C',
      border: true,
      },
  });

  // Line Items
  obj.cart_items.forEach((item) => {
      result.push({
      row: [
          { cellText: item.line_id },
          { cellText: item.domestic_material_only ? 'Yes' : 'No' },
          { cellText: item.description },
          { cellText: item.qty },
          { cellText: item.qty_unit.toUpperCase() },
          { cellText: item.buyer_line_total },
      ],
      style: baseRowStyle,
      });
  });

  // Empty row
  result.push({ row: [{ cellText: ' ' }] });

  // Total Weight
  result.push({
      row: [
      { cellText: 'Total Weight', style: baseHeaderStyle },
      { cellText: obj.total_weight + ' LBs' },
      ],
      style: baseRowStyle,
  });

  // Material Total
  result.push({
      row: [
      { cellText: 'Material Total', style: baseHeaderStyle },
      { cellText: '$ ' + obj.buyer_po_price },
      ],
      style: baseRowStyle,
  });

  // Sales Tax
  result.push({
      row: [
      { cellText: 'Sales Tax', style: baseHeaderStyle },
      { cellText: '$ ' + obj.sales_tax },
      ],
      style: baseRowStyle,
  });

  // Subscription
  result.push({
      row: [
      { cellText: 'Subscription', style: baseHeaderStyle },
      { cellText: '$ ' + obj.subscription },
      ],
      style: baseRowStyle,
  });

  // Total Purchase
  result.push({
      row: [
      { cellText: 'Total Purchase', style: baseHeaderStyle },
      {
          cellText:
          '$ ' +
          (
              Number(obj.subscription) +
              Number(obj.sales_tax) +
              Number(obj.buyer_po_price)
          ).toFixed(2),
      },
      ],
      style: baseRowStyle,
  });

  return result;
}
export const calculateSubscriptionAmount = (userCount: number = 0, pricingTiers: PricingTier[] | null | undefined): PricingTier | null => {
  if (!pricingTiers?.length) {
      return null;
  }

  try {
      const tier = pricingTiers.find(tier => {
          const isAboveMinimum = tier.min_user_count <= userCount;
          const isBelowMaximum = tier.max_user_count === null || userCount <= tier.max_user_count;
          return isAboveMinimum && isBelowMaximum;
      });

      if (!tier) {
          return null;
      }

      return tier;

  } catch (error) {
      console.error('Error calculating subscription amount:', error);
      return null;
  }
};

export const calculateLineWeight = (data) => {
  let lineWeight = 0;
  const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
  if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0){
      const qtyUnit = data.qty_unit ? data.qty_unit : data.descriptionObj.QUM_Dropdown_Options.split(",")[0];
      const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`]?.replace(/[\$,]/g, '')) || 0
      const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`]?.replace(/[\$,]/g, '')) || 0;
      const lbsPerFt = orderIncrementLb / orderIncrementFt
      const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
      const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${qtyUnit.toLowerCase()}`]?.replace(/[\$,]/g, '')) || 0;
      lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
  }
  return formatToTwoDecimalPlaces(lineWeight);
}
export const formatDraftFinalPayload = (localDraftData: any, draftId: string, initialData: any = null, isConvertingPo: boolean|undefined = undefined) => {
    const retValue = {
      "id": draftId,
      "bom_id": localDraftData.bom_id || undefined,
      "buyer_internal_po": localDraftData.buyer_internal_po || undefined,
      "shipping_details": {
        "zip": localDraftData.shipping_details.zip || undefined,
        "line1": localDraftData.shipping_details.line1 || undefined,
        "line2": localDraftData.shipping_details.line2 || null,
        "city": localDraftData.shipping_details.city || undefined,
        "state_id": localDraftData.shipping_details.state_id || undefined,
        "delivery_address_id": localDraftData.shipping_details.delivery_address_id || undefined
      },
      "buyer_po_price": localDraftData.buyer_po_price || undefined,
      "sales_tax": localDraftData.sales_tax || undefined,
      "freight_term": "Delivered",
      "order_type": localDraftData.order_type || ((location.pathname === routes.createPoPage) ? orderType.PO : orderType.QUOTE),  //change this to 'PO' if on purchasing screen 
      "source": localDraftData.source || null,
      "deposit_amount": localDraftData.deposit_amount || undefined,
      "delivery_date": localDraftData.delivery_date || undefined,
      "total_weight": localDraftData.total_weight || undefined,
      "cart_items": localDraftData.cart_items ? formattedCartItems(localDraftData.cart_items, draftId, initialData) : undefined,
      "payment_method": localDraftData.payment_method || null,
      "processing_fees": localDraftData.processing_fees || undefined,
      "is_converting_po": isConvertingPo || undefined
    }
    const buyerSetting = useBuyerSettingStore.getState().buyerSetting;
    if(retValue?.shipping_details?.delivery_address_id){
      let defaultAddress = buyerSetting?.delivery_address?.find((address: any) => address.id === retValue?.shipping_details?.delivery_address_id);
      if(defaultAddress){
          const isAddressChanged = checkAddressChanged(retValue?.shipping_details, defaultAddress);
          if(isAddressChanged){
              retValue.shipping_details.delivery_address_id = null;
          }
      }
  }

    if (retValue?.cart_items?.length === 0) {
      retValue.cart_items = null;
    }

    return retValue;
}


export const formattedCartItems = (cartItems: any, draftId: boolean | string = false, initialData: any = null) => {
  return cartItems
    .filter((item: any) => (item?.id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED"))
    .map((item, index) => {
      if ((item?.id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED")) {
        const buyerPricingLbKey = `${newPricingPrefix}${priceUnits.lb}`;
        let formattedQtyUnit = null;
        let formattedPriceUnit = null;
        if (draftId && item?.id && !item?.qty_unit) {
          formattedQtyUnit = initialData?.cart_items[index]?.qty_unit
        } else {
          formattedQtyUnit = item?.qty_unit?.toUpperCase();
        }

        if (draftId && item?.id && !item?.price_unit) {
          formattedPriceUnit = initialData?.cart_items[index]?.price_unit
        } else {
          formattedPriceUnit = item?.price_unit?.toUpperCase();
        }
        let priceListObj: any = {};
        if (item?.descriptionObj) {
          Object.keys(item?.descriptionObj).forEach(key => {
            if (key.includes(newPricingPrefix)) {
              priceListObj[key.replace(newPricingPrefix, '').toLowerCase()] = item?.descriptionObj[key];
            }
          })
        }
        const priceList = item?.descriptionObj ? priceListObj : null;
        const cartItem = {
          "line_id": index + 1,
          "description": item?.descriptionObj?.UI_Description ?? null,
          "qty": (draftId && item?.id && !item?.qty && initialData?.cart_items[index]?.qty) ? initialData?.cart_items[index]?.qty : (item?.qty) ? item?.qty : null,
          "qty_unit": formattedQtyUnit || null,
          "product_tag": item?.product_tag ?? null,
          "product_tag_mapping_id": item?.descriptionObj?.product_tag ?? null,
          "buyer_price_per_unit": (draftId && item?.id && !item?.buyer_price_per_unit) ? initialData?.cart_items[index]?.price_per_unit : item?.buyer_price_per_unit ?? null,
          "price_unit": formattedPriceUnit || null,
          "buyer_line_total": item?.buyer_line_total ?? null,
          "product_id": (draftId && item?.id && !item?.descriptionObj?.Product_ID) ? initialData?.cart_items[index]?.product_id : item?.descriptionObj?.Product_ID ?? null,
          "reference_product_id": item?.descriptionObj?.id ?? null,
          "shape": item?.descriptionObj?.Key2 ?? null,
          "domestic_material_only": item?.domesticMaterialOnly ?? false,
          "buyer_calculation_price": item?.buyer_calculation_price ?? null,
          "line_status": item?.line_status ?? null,
          "id": draftId ? item?.id : undefined,
          "is_line_removed": draftId ? (!item?.descriptionObj?.Product_ID ? true : false) : undefined,
          "prices_list": priceList
        };
        return cartItem
      }
      return null
    });
}

export const expiredActivePricedProducts = async (expirePricingMutation: any) => {
    const savedSearchProducts = useSearchStore.getState().savedSearchProducts;
    const activePricedProducts = savedSearchProducts.filter((item: any) => {
      const pricingExpired = item?.pricing_expired;
      return pricingExpired !== undefined &&
        (pricingExpired === false || pricingExpired === 0);
    });
      if (activePricedProducts.length > 0) {
        const payload = {
            "data": activePricedProducts.map((item: any) => item.id)
        }
        try {
            const res = await expirePricingMutation(payload);
            console.log("res @>>>>>>>", res);
        } catch (error) {
            console.log("error @>>>>>>>", error);
        }
    }
}

  // Helper function to validate routing number
  export const validateRoutingNumber = (routingNumber: string): boolean => {
    // Check if it's exactly 9 digits
    if (!/^\d{9}$/.test(routingNumber)) {
      return false;
    }

    // Perform checksum validation
    const digits = routingNumber.split('').map(Number);
    const sum =
      3 * (digits[0] + digits[3] + digits[6]) +
      7 * (digits[1] + digits[4] + digits[7]) +
      (digits[2] + digits[5] + digits[8]);

    return sum % 10 === 0;
  };

  // Helper function to validate account number
  export const validateAccountNumber = (accountNumber: string): boolean => {
    // Check if it contains only digits
    if (!/^\d+$/.test(accountNumber)) {
      return false;
    }

    // Check length (typically between 8-17 digits)
    return accountNumber.length >= 8 && accountNumber.length <= 17;
  };

  // Utility function to format payment date from "2025-10-01" to "Sep 1, 2025"
export const formatDateForDisplay  = (dateString: string | undefined): string => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid date
    
    const options: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    };
    
    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    return dateString; // Return original if parsing fails
  }
};


  // ---- Helpers ----
  export function formatWhen(dtStr: string): string {
    const dt = new Date(dtStr.replace(" ", "T"));
    const now = new Date();
    const isSameDay =
      dt.getFullYear() === now.getFullYear() &&
      dt.getMonth() === now.getMonth() &&
      dt.getDate() === now.getDate();
    const yday = new Date(now);
    yday.setDate(now.getDate() - 1);
    const isYesterday =
      dt.getFullYear() === yday.getFullYear() &&
      dt.getMonth() === yday.getMonth() &&
      dt.getDate() === yday.getDate();
  
    const time = dt.toLocaleTimeString([], { hour: "numeric", minute: "2-digit" });
    if (isSameDay) return time;
    if (isYesterday) return `Yesterday ${time}`;
    return dt.toLocaleDateString([], { weekday: "long" });
  }

  export function getFormattedCentralTime() {
  const now = new Date();

  // Format in Central Time
  const options = {
    timeZone: "America/Chicago",  // Central Time
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  };

  const formatter = new Intl.DateTimeFormat("en-US", options);
  const parts = formatter.formatToParts(now);

  // Extract parts
  const dateParts = {};
  parts.forEach(p => { dateParts[p.type] = p.value; });

  // Build output: YYYY-MM-DD hh:mm AM/PM
  return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute} ${dateParts.dayPeriod}`;
}

export const formattedDate = (() => {
    const d = new Date();
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const dd = String(d.getDate()).padStart(2, '0');
    const yy = String(d.getFullYear()).slice(-2);
    return `${mm}-${dd}-${yy}`;
  })();


export const createActionUrl = (navigationSchema, notificatonId) => {
    const userId = useGlobalStore.getState().userData.data.id;
    let actionUrl = `bryzos://${userId}/${notificatonId}/`;
    if(navigationSchema){
        if(navigationSchema.routePath)actionUrl+=`${navigationSchema.routePath}`;
        const state = navigationSchema.state;
        if(state){
            let stateUrl = '?';
            for(const key in state){
                stateUrl+=`${key}=${state[key]},`;
            }
            actionUrl+=`${stateUrl.substring(0,stateUrl.length-1)}`;
        }
    }
    return actionUrl;
}

export const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const checkAddressChanged = (data: any, defaultData: any) => {
  // Helper function to normalize values (treat empty string and null as equivalent)
  const normalizeValue = (value: any) => {
    return value === '' || value === null || value === undefined ? null : value;
  };

  return normalizeValue(data?.line1) !== normalizeValue(defaultData?.line1) ||
    normalizeValue(data?.line2) !== normalizeValue(defaultData?.line2) ||
    normalizeValue(data?.city) !== normalizeValue(defaultData?.city) ||
    normalizeValue(data?.state_id) !== normalizeValue(defaultData?.state_id) ||
    normalizeValue(data?.zip) !== normalizeValue(defaultData?.zip);
}

export const updateDraftDataWhenSwitchingToBomExtractor = () => {
  const selectedQuote = useCreatePoStore.getState().selectedQuote;
  const setSelectedQuote = useCreatePoStore.getState().setSelectedQuote;
  const quoteList = useCreatePoStore.getState().quoteList;
  const setQuoteList = useCreatePoStore.getState().setQuoteList;
  const purchasingList = useCreatePoStore.getState().purchasingList;
  const setPurchasingList = useCreatePoStore.getState().setPurchasingList;

  if (selectedQuote?.id && selectedQuote?.cart_items?.length > 0) {
    const updatedCartItems = selectedQuote?.id.includes(noIdGeneric) ? null : selectedQuote?.cart_items?.map((item: any) => {
      return {
        ...item,
        descriptionObj: {}
      }
    })
    const _selectedQuote = {
      ...selectedQuote,
      cart_items: updatedCartItems
    }
    setSelectedQuote(_selectedQuote);
    if (_selectedQuote.order_type === orderType.QUOTE) {
      setLocal(localStorageKeys.poQuoting, _selectedQuote);
      const _quoteList = quoteList.map((quote: any) => {
        if (quote.id === _selectedQuote.id) {
          return _selectedQuote;
        }
        return quote;
      });
      setQuoteList(_quoteList);
    } else {
      setLocal(localStorageKeys.poPurchasing, _selectedQuote);
      const _purchasingList = purchasingList.map((quote: any) => {
        if (quote.id === _selectedQuote.id) {
          return _selectedQuote;
        }
        return quote;
      });
      setPurchasingList(_purchasingList);
    }
  }
}


export const showDraftDataUsingId = async (uploadedBomData: any, getDraftLines: any) => {
  const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
  const updatedDraftId = useCreatePoStore.getState().updatedDraftId;
  try{
    const quoteList = useCreatePoStore.getState().quoteList;
    const purchasingList = useCreatePoStore.getState().purchasingList;
    let draftData = null;
    let draftId = uploadedBomData?.id;
    if(!draftId || draftId.includes(noIdGeneric)) draftId = updatedDraftId?.id;
    
    if(uploadedBomData?.order_type === orderType.QUOTE){
      const quote = quoteList.find((quote: any) => quote.id === draftId);
      if(quote){
        draftData = quote;
      }
    } else if(uploadedBomData?.order_type === orderType.PO){
      const purchasing = purchasingList.find((purchasing: any) => purchasing.id === draftId);
      if(purchasing){
        draftData = purchasing;
      }
    }
    if(draftData){
      const draftLines = await getDraftLines(draftData.id);
      updateQuoteData(draftData, draftLines?.data);
    }
  } catch(error){
    console.error("error @>>>>>>>", error);
    showCommonDialog(null, commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
  }
}

export const updateQuoteData = (quoteData: any, quoteLinesData: any) => {
  const productMapping = useGlobalStore.getState().productMapping;
  const setSelectedQuote = useCreatePoStore.getState().setSelectedQuote;
  const setCreatePoData = useCreatePoStore.getState().setCreatePoData;

  let createPoData: any = {
    ...quoteData,
    id: quoteData.id,
    isEdit: !quoteData.pricing_expired,
    buyer_internal_po: quoteData.buyer_internal_po,
    delivery_date: quoteData.delivery_date,
    shipping_details: quoteData.shipping_details,
    order_type: quoteData.order_type,
    buyer_po_price: quoteData.buyer_po_price,
    created_date: quoteData.created_date,
    deposit_amount: quoteData.deposit_amount,
    freight_amount: quoteData.freight_amount,
    last_price_updated_date: quoteData.last_price_updated_date,
    payment_method: quoteData.payment_method,
    pricing_expired: quoteData.pricing_expired,
    sales_tax: quoteData.sales_tax,
    source: quoteData.source,
    subscription: quoteData.subscription,
    total_weight: quoteData.total_weight
  }
  // const headerInfo = {
  // }
  // createPoData.headerInfo = headerInfo;
  createPoData.cart_items = quoteLinesData?.map((line: any) => {
    return {
      ...line,
      descriptionObj: productMapping[line.product_id]
    }
  });
  setSelectedQuote(createPoData);
  setCreatePoData(createPoData);
}

export const getDeliveryDateData = async (deliveryAddressId?: string | null, getDeliveryDate?: any) => {
  try {
    const buyerSetting = useBuyerSettingStore.getState().buyerSetting;
    const setDeliveryDateMap = useBuyerSettingStore.getState().setDeliveryDateMap;
    const setDeliveryAllowedDates = useBuyerSettingStore.getState().setDeliveryAllowedDates;
    const setDisableDeliveryDate = useBuyerSettingStore.getState().setDisableDeliveryDate;
    if (!deliveryAddressId) {
      const deliveryAddressDataFromSetting = buyerSetting?.delivery_address || [];
      deliveryAddressId = (deliveryAddressDataFromSetting?.find((address: any) => address.is_default) || deliveryAddressDataFromSetting[0])?.id;
    }
    let deliveryDateList = [];
    const payload = {
      id: deliveryAddressId
    }
    const res = await getDeliveryDate.mutateAsync(payload);
    if (res?.data?.data) {
      deliveryDateList = res.data.data
    }
    const optionMap = deliveryDateList.reduce((acc, option) => {
      acc[option.value] = option;
      return acc;
    }, {});
    setDeliveryDateMap(optionMap);
    const allowedDates = deliveryDateList
          .filter(date => !date?.disabled)
          .map(date => new Date(date?.value));
    setDeliveryAllowedDates(allowedDates);

    let disableDeliveryDates = true;
    deliveryDateList.forEach(deliveryDateObj => {
      // if(deliveryDateObj.days_to_add === deliveryDaysAddValue){
      //     const deliveryDate = !deliveryDateObj.disabled ? deliveryDateObj.value : null;
      //     setValue('delivery_date', dayjs(deliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit));
      // }
      if (!deliveryDateObj.disabled && disableDeliveryDates) {
        disableDeliveryDates = false;
      }
    });
    setDisableDeliveryDate(disableDeliveryDates)
    return optionMap
  } catch (error) {
    console.error("err @>>>>>>>", error)
    return []
  }
}


/**
 * Extracts unique item shapes from an order and returns them sorted alphabetically
 * @param order - The order object containing items
 * @returns Alphabetically sorted array of unique shapes
 */
export const getSortedUniqueShapes = (order: any): string[] => {
    if (!order?.items || !Array.isArray(order.items)) {
      return [];
    }
    
    const distinctShapes = new Set<string>();
    
    order.items.forEach((item) => {
      if (item.shape && typeof item.shape === 'string') {
        distinctShapes.add(item.shape.trim());
      }
    });
    
    return Array.from(distinctShapes).sort((a, b) => 
      a.localeCompare(b, undefined, { sensitivity: 'base' })
    );
  };
  
  /**
   * Creates a formatted header string from sorted unique shapes
   * @param sortedShapes - Array of sorted unique shapes
   * @param maxLength - Maximum length before truncation (optional)
   * @returns Formatted header string
   */
  export const createShapeHeader = (sortedShapes: string[], maxLength?: number): string => {
    if (!sortedShapes.length) return '';
    
    let header = sortedShapes.join(', ');
    
    if (maxLength && header.length > maxLength) {
      header = `${header.substring(0, maxLength)}...`;
    }
    
    return header;
  };
  
  /**
   * Combined utility to get sorted header directly from order
   * @param order - The order object
   * @param maxLength - Maximum length before truncation (optional)
   * @returns Formatted, sorted header string
   */
  export const getSortedShapeHeader = (order: any, maxLength?: number): string => {
    const sortedShapes = getSortedUniqueShapes(order);
    return createShapeHeader(sortedShapes, maxLength);
  };


  /**
 * Gets a sort key for alphabetical sorting by product shapes
 * @param order - The order object
 * @returns A string key for sorting (lowercase, comma-separated sorted shapes)
 */
export const getOrderShapeSortKey = (order: any): string => {
    const sortedShapes = getSortedUniqueShapes(order);
    return sortedShapes.join(',').toLowerCase();
  };
  
  /**
   * Compares two orders for alphabetical sorting by product shapes
   * @param orderA - First order
   * @param orderB - Second order
   * @returns -1, 0, or 1 for sorting
   */
  export const compareOrdersByShape = (orderA: any, orderB: any): number => {
    const keyA = getOrderShapeSortKey(orderA);
    const keyB = getOrderShapeSortKey(orderB);
    return keyA.localeCompare(keyB);
  };

  /**
 * Calculates the total order value (material + sales tax)
 * @param order - The order object
 * @returns Total order value as number
 */
export const getTotalOrderValue = (order: any): number => {
    if (!order) return 0;
    
    const materialValue = Number(order.seller_po_price) || 0;
    const salesTaxValue = Number(order.seller_sales_tax) || 0;
    
    return materialValue + salesTaxValue;
  };
  
  /**
   * Gets sort key for amount-based sorting
   * @param order - The order object
   * @returns Total order value for sorting
   */
  export const getOrderAmountSortKey = (order: any): number => {
    return getTotalOrderValue(order);
  };
  
  /**
   * Compares two orders by total value for highest sorting
   * @param orderA - First order
   * @param orderB - Second order
   * @returns Comparison result for sorting
   */
  export const compareOrdersByAmountHighest = (orderA: any, orderB: any): number => {
    const valueA = getTotalOrderValue(orderA);
    const valueB = getTotalOrderValue(orderB);
    return valueB - valueA; // Descending for highest first
  };
  
  /**
   * Compares two orders by total value for lowest sorting
   * @param orderA - First order
   * @param orderB - Second order
   * @returns Comparison result for sorting
   */
  export const compareOrdersByAmountLowest = (orderA: any, orderB: any): number => {
    const valueA = getTotalOrderValue(orderA);
    const valueB = getTotalOrderValue(orderB);
    return valueA - valueB; // Ascending for lowest first
  };

  export function getNestedValue(obj: any, path: string) {
    return path.split('.').reduce((acc, key) => acc?.[key], obj);
  }
  
  export function isValidValue(value: any) {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim() !== '';
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object') return Object.keys(value).length > 0;
    return true; // numbers, booleans etc.
  }
/**
 * Formats a number by removing .00 decimals while preserving other decimal values
 * @param value - The number to format (can be string or number)
 * @returns Formatted number as string
 */
export const removeDecimalZero = (value: string | number): string => {
  if (value === null || value === undefined || value === '') {
    return '';
  }
  
  // Convert to string if it's a number
  const stringValue = String(value);
  
  // Check if the number has decimals
  if (stringValue.includes('.')) {
    // If it ends with .00, remove the .00
    if (stringValue.endsWith('.00')) {
      return stringValue.replace(/\.00$/, '');
    }
    // If it has other decimals, return as is
    return stringValue;
  }
  
  // If no decimals, return as is
  return stringValue;
};

// TypeScript (works in Node or browser)
export function setIntervalPromise<T>(
  fn: () => T | Promise<T>,
  ms: number
): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const id: ReturnType<typeof setInterval> = setInterval(async () => {
      try {
        const result = await fn();
        clearInterval(id);
        resolve(result);
      } catch (err) {
        clearInterval(id);
        reject(err);
      }
    }, ms);
  });
}


export function convertUtcToLocalTime(
  utcDateStr,
  inputFormat = 'YYYY-MM-DD HH:mm:ss',
  outputFormat = 'YYYY-MM-DD HH:mm:ss'
) {
  // Create isolated dayjs instance with plugins applied
  const dayjs = dayjsFactory
    .extend(utc)
    .extend(timezone);

  return dayjs.utc(utcDateStr, inputFormat).local().format(outputFormat);

}

export function formatDisplayDateForTemplate(date, format = 'M/D/YYYY @ h:mma') {
  const dayjs = dayjsFactory; // no plugins needed here
  return dayjs(date).format(format);
}

export const newFormatCartItems = (products, draftId = null, initialData: any = null) => {
  return products
    .filter((item: any) => draftId ? (item?.draft_line_id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0)) : ((item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED"))
    .map((item, index) => {
      if (draftId ? (item?.draft_line_id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0)) : ((item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED")) {
        const buyerPricingLbKey = `${newPricingPrefix}${priceUnits.lb}`;
        let formattedQtyUnit = '';
        let formattedPriceUnit = '';
        if (draftId && item?.draft_line_id && !item?.qty_unit) {
          formattedQtyUnit = initialData?.cart_items[index]?.qty_unit
        } else {
          formattedQtyUnit = item?.qty_unit?.toUpperCase();
        }

        if (draftId && item?.draft_line_id && !item?.price_unit) {
          formattedPriceUnit = initialData?.cart_items[index]?.price_unit
        } else {
          formattedPriceUnit = item?.price_unit?.toUpperCase();
        }
        const cartItem = {
          "id": draftId ? item?.draft_line_id : item?.id || undefined,
          "line_id": index + 1,
          "product_id": (draftId && item?.draft_line_id && !item?.descriptionObj?.Product_ID) ? initialData?.cart_items[index]?.product_id : item?.descriptionObj?.Product_ID ?? '',
          "qty": (draftId && item?.draft_line_id && !item?.qty) ? initialData?.cart_items[index]?.qty : item?.qty || '',
          "qty_unit": formattedQtyUnit,
          "price_unit": formattedPriceUnit,
          "product_tag": item?.product_tag || '',
          "domestic_material_only": item?.domesticMaterialOnly || false,
          "line_status": item?.line_status || undefined,
          "is_line_removed": draftId ? (!item?.descriptionObj?.Product_ID ? true : false) : undefined
        };
        return cartItem
      }
      return null
    });
}

export const updateOrderManagementData = (quoteData: any, rightWindowData: any) => {
  const productMapping = useGlobalStore.getState().productMapping;
  const setSelectedQuote = useCreatePoStore.getState().setSelectedQuote;
  const setCreatePoData = useCreatePoStore.getState().setCreatePoData;
  const setOrderManageMentInitialData = useOrderManagementStore.getState().setOrderManageMentInitialData;
  const setOrderToBeShownInOrderAccept = useSellerOrderStore.getState().setOrderToBeShownInOrderAccept;

  const shippingDetails = quoteData?.shipping_details || {
    city: quoteData?.city,
    state_id: quoteData?.state_id,
    state_code: quoteData?.state_code,
    zip: quoteData?.zip,
    line1: quoteData?.line1,
    delivery_address_id: quoteData?.delivery_address_id,
    delivery_address_line1: quoteData?.delivery_address_line1,
    delivery_address_line2: quoteData?.delivery_address_line2,
  };
  let createPoData: any = {
    ...quoteData,
    ...rightWindowData,
    id: quoteData.id,
    bom_id: quoteData.bom_id,
    buyer_po_number: quoteData.buyer_po_number,
    isEdit: false,
    buyer_internal_po: quoteData.buyer_internal_po,
    delivery_date: quoteData.delivery_date,
    shipping_details: shippingDetails,
    order_type: quoteData.order_type,
    buyer_po_price: quoteData.seller_po_price || quoteData.buyer_po_price || quoteData.material_total || '0',
    created_date: quoteData.created_date,
    deposit_amount: quoteData.deposit_amount || quoteData.deposit || '0',
    freight_amount: quoteData.freight_amount,
    last_price_updated_date: quoteData.last_price_updated_date,
    payment_method: quoteData.payment_method,
    pricing_expired: quoteData.pricing_expired,
    sales_tax: quoteData.sales_tax,
    source: quoteData.source,
    subscription: quoteData.subscription,
    total_weight: quoteData.total_weight,
    processing_fees: quoteData.processing_fees || '0'
  }
  createPoData.cart_items = createPoData?.cart_items ? createPoData?.cart_items?.map((line: any) => {
    let product = productMapping[line.product_id];
    if (line.counter_id && !line?.product_id) {
      const disputeCounterData = line.line_dispute_counter[0];
      // const disputeCounterData = line.line_dispute_counter?.find((dispute: any) => dispute.counter_status === "ORIGINAL");
      if (disputeCounterData) {
        product = productMapping[disputeCounterData?.product_id];
        line = {...line, ...disputeCounterData};
      }
    }
    const qtyUm = product?.QUM_Dropdown_Options?.split(",")?.map((item: any) => item.toLowerCase());
    const priceUm = product?.PUM_Dropdown_Options?.split(",")?.map((item: any) => item.toLowerCase());

    line.line_dispute_counter = line?.line_dispute_counter ? line?.line_dispute_counter?.map((disputeLine: any) => {
      let product = productMapping[disputeLine.product_id];
      const qtyUm = product?.QUM_Dropdown_Options?.split(",")?.map((item: any) => item.toLowerCase());
      const priceUm = product?.PUM_Dropdown_Options?.split(",")?.map((item: any) => item.toLowerCase());
      return {
        ...disputeLine,
        descriptionObj: product,
        price_unit: disputeLine.price_unit?.toLowerCase(),
        qty_unit: disputeLine.qty_unit?.toLowerCase(),
        qty_um: qtyUm ?? [],
        price_um: priceUm ?? [],
        buyer_price_per_unit: disputeLine.seller_price_per_unit || disputeLine.buyer_price_per_unit || disputeLine.price_per_unit || "0",
        buyer_line_total: disputeLine.seller_line_total || disputeLine.buyer_line_total || disputeLine.line_total || "0"
      }
    }) : null;

    return {
      ...line,
      descriptionObj: product,
      price_unit: line.price_unit?.toLowerCase(),
      qty_unit: line.qty_unit?.toLowerCase(),
      qty_um: qtyUm ?? [],
      price_um: priceUm ?? [],
      buyer_price_per_unit: line.seller_price_per_unit || line.buyer_price_per_unit || line.price_per_unit || "0",
      buyer_line_total: line.seller_line_total || line.buyer_line_total || line.line_total || "0"
    }
  }) : [];
  setOrderToBeShownInOrderAccept({});
  setSelectedQuote(createPoData);
  setCreatePoData(createPoData);
  setOrderManageMentInitialData(structuredClone(createPoData));
}

export const handleOrderManagementNavigation = (target: any, currentPath: any, handleAction: (...args: any[]) => void, actionParams?: any[]) => {
  const isEditingPo = useOrderManagementStore.getState().isEditingPo;
  const showCommonDialog = useDialogStore.getState().showCommonDialog;
  const resetDialogStore = useDialogStore.getState().resetDialogStore;
  const selectedQuote = useCreatePoStore.getState().selectedQuote;
  const setIsNavigatingToOtherPage = useOrderManagementStore.getState().setIsNavigatingToOtherPage;

  if (isEditingPo && currentPath === routes.orderManagementPage && !selectedQuote?.seller_name) {
    showCommonDialog(null, "Navigating away will cancel this order and save it as a purchase draft. Do you want to continue?", null, resetDialogStore, [
      {
        name: 'Yes',
        action: () => {
          setIsNavigatingToOtherPage(true);
          setTimeout(() => {
            if (actionParams) {
              handleAction(...actionParams);
            } else {
              handleAction(target);
            }
            resetOrderRelatedData();
            resetDialogStore();
          }, 100);
        }
      },
      {
        name: 'No',
        action: () => resetDialogStore()
      }
    ]);
  } else {
    if (actionParams) {
      handleAction(...actionParams);
    } else {
      handleAction(target);
    }
    resetOrderRelatedData();
  }
}

export const resetOrderRelatedData = () => {
  const setSelectedQuote = useCreatePoStore.getState().setSelectedQuote;
  const setCreatePoData = useCreatePoStore.getState().setCreatePoData;
  const setBomData = useBomPdfExtractorStore.getState().setBomData;
  const setOrderManageMentInitialData = useOrderManagementStore.getState().setOrderManageMentInitialData;
  
  setSelectedQuote(null);
  setCreatePoData(null);
  setBomData(null);
  setOrderManageMentInitialData(null);
}

export const saveDraftData = async (postDraftPo, localKey, initialData, isConvertingPo: boolean | undefined = undefined) => {
  const showCommonDialog = useDialogStore.getState().showCommonDialog;
  const resetDialogStore = useDialogStore.getState().resetDialogStore;
  const setUpdatedDraftId = useCreatePoStore.getState().setUpdatedDraftId;
  const setIsConvertingToPo = useCreatePoStore.getState().setIsConvertingToPo;
  const setIsNavigatingToOtherPage = useOrderManagementStore.getState().setIsNavigatingToOtherPage;
  const isNavigatingToOtherPage = useOrderManagementStore.getState().isNavigatingToOtherPage;
  try {
    const localDraftData = getLocal(localKey, null);
    if (localDraftData) {
      if (getLocal(localStorageKeys.isSaving, false)) {
        return;
      }
      setLocal(localStorageKeys.isSaving, true);
      const draftId = (localDraftData)?.id?.includes(noIdGeneric) ? undefined : (localDraftData)?.id;
      const payload = formatDraftFinalPayload(localDraftData, draftId, initialData, isConvertingPo);
      setUpdatedDraftId(localDraftData);
      const response = await postDraftPo.mutateAsync(payload);
      clearLocal(localKey);
      setIsNavigatingToOtherPage(false);
      return response;
    }
  } catch (error) {
    setIsConvertingToPo(null);
    if (error?.message === 'Unable to retrieve the draft.The draft may not exist or is inactive') {
      clearLocal(localKey);
      setIsNavigatingToOtherPage(false);
      return;
    }
    if (!isNavigatingToOtherPage) {
      showCommonDialog(null, commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
    }
    console.error("error @>>>>>>>", error);
  } finally {
    setTimeout(() => {
      setLocal(localStorageKeys.isSaving, false);
    },
      3000);
  }
}

export const downloadCertificate = async (url: string) => {
  const showCommonDialog = useDialogStore.getState().showCommonDialog;
  const resetDialogStore = useDialogStore.getState().resetDialogStore;
  const setShowLoader = useGlobalStore.getState().setShowLoader;
  try {
    setShowLoader(true)
    const res = await axios.get(url)
    if (res.data.data) {
      const url = res.data.data
      const fileName = url.split('/')
      await downloadFiles(url, fileName[fileName.length - 1], fileType.pdf)
    }
  } catch (e) {
    showCommonDialog(null, commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
  } finally {
    setShowLoader(false);
  }
}

export const handleDraftPoSave = async (draftData: any, postDraftPo: any,queryClient: any , initialData: any, isConvertingPo: boolean|undefined = undefined) => {
  const setUpdatedDraftId = useCreatePoStore.getState().setUpdatedDraftId;
  const setIsConvertingToPo = useCreatePoStore.getState().setIsConvertingToPo;
  const showCommonDialog = useDialogStore.getState().showCommonDialog;
  const resetDialogStore = useDialogStore.getState().resetDialogStore;

  try{
      if (getLocal(localStorageKeys.isSaving, false)) {
          return;
      }
      setLocal(localStorageKeys.isSaving, true);
      const draftId = draftData.id.includes(noIdGeneric) ? undefined : draftData.id;
      const payload = formatDraftFinalPayload(draftData, draftId, initialData, isConvertingPo);
      setUpdatedDraftId(draftData);
      const response = await postDraftPo.mutateAsync(payload);
      if(response?.data?.data?.error_message){
          setIsConvertingToPo(null);
          showCommonDialog(null, response?.data?.data?.error_message || commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
          return;
      }
      queryClient.invalidateQueries([reactQueryKeys.getUserPartData]);
      clearLocal(localStorageKeys.poQuoting);
      clearLocal(localStorageKeys.poPurchasing);
      return response;
  }catch(error){
      setIsConvertingToPo(null);
      if(error?.message === 'Unable to retrieve the draft.The draft may not exist or is inactive'){
          clearLocal(localStorageKeys.poQuoting);
          clearLocal(localStorageKeys.poPurchasing);
          return;
      }
      showCommonDialog(null, commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
      console.error("error @>>>>>>>", error);
  } finally {
      setTimeout(() => {
          setLocal(localStorageKeys.isSaving, false);
      }, 
      3000);
  }
}
export const formatAndUpdateDraftPoData = (draftData: any, draftLinesData: any) => {
  const isBuyerDeletedItemsPage = location.pathname === routes.buyerDeleteOrderPage;
  const productMapping = useGlobalStore.getState().productMapping;
  const setSelectedQuote = useCreatePoStore.getState().setSelectedQuote;
  const setCreatePoData = useCreatePoStore.getState().setCreatePoData;

  let createPoData: any = {
    ...draftData,
    id: draftData.id,
    bom_id: draftData.bom_id,
    isEdit: isBuyerDeletedItemsPage ? false : !draftData.pricing_expired,
    buyer_internal_po: draftData.buyer_internal_po,
    delivery_date: draftData.delivery_date,
    shipping_details: draftData.shipping_details,
    order_type: draftData.order_type,
    buyer_po_price: draftData.buyer_po_price,
    created_date: draftData.created_date,
    deposit_amount: draftData.deposit_amount,
    freight_amount: draftData.freight_amount,
    last_price_updated_date: draftData.last_price_updated_date,
    payment_method: draftData.payment_method,
      pricing_expired: draftData.pricing_expired,
    sales_tax: draftData.sales_tax,
    source: draftData.source,
    subscription: draftData.subscription,
    total_weight: draftData.total_weight,
    processing_fees: draftData.processing_fees || draftData.processing_fees || '0'
  }
  createPoData.cart_items = draftLinesData?.map((line: any) => {
    const product = productMapping[line.product_id];
    const qtyUm = product?.QUM_Dropdown_Options?.split(",")?.map((item: any) => item.toLowerCase());
    const priceUm = product?.PUM_Dropdown_Options?.split(",")?.map((item: any) => item.toLowerCase());
    return {
      ...line,
      descriptionObj: product,
      price_unit: line.price_unit?.toLowerCase(),
      qty_unit: line.qty_unit?.toLowerCase(),
      qty_um: qtyUm ?? [],
      price_um: priceUm ?? [],
      buyer_price_per_unit: line.buyer_price_per_unit || line.price_per_unit || "0",
      buyer_line_total: line.buyer_line_total || line.line_total || "0"
    }
  });
  setSelectedQuote(createPoData);
  setCreatePoData(createPoData);

}

export const formatDisputePayload = (dataLevel: string, dataType: string, data: any, action: string) => {
  const orderManageMentInitialData = useOrderManagementStore.getState().orderManageMentInitialData;
  console.log("data @>>>>>>>", data);
  let payload: any = {
    po_number: orderManageMentInitialData?.buyer_po_number,
    order_level: {},
    order_line_level: []

  };
  if(dataLevel === "orderLevel"){
    if(dataType === "deliver_by"){
      payload.order_level.deliver_by = {
        delivery_date: data?.delivery_date,
        counter_id: data?.counter_id,
        action
      }
    }
    if(dataType === "deliver_to"){
      payload.order_level.deliver_to = {
        line1: data?.line1,
        city: data?.city,
        state_id: data?.state_id,
        zip: data?.zip,
        counter_id: data?.counter_id,
        action
      }
    }
    if(dataType === "cancelOrder"){
      payload.order_level.is_order_canceled = 1;
      payload.order_level.action = action;
      payload.order_level.counter_id = data?.counter_id;
      payload.order_level.restocking_fee = data?.restocking_fee;
    }
  }
  return payload;
}