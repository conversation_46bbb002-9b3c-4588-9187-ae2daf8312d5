.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
  
}

#root{
  height: 100%;
} 

.widgetCanvas {
  display: inline-flex;
  position: relative;
  background: transparent;
  // border-radius: 16px;
  width: 100%;
  overflow: hidden;
  flex:1;
  height: calc(100vh - 32px);
}

.webBackground {
  background-color: #000;
}

.webLoginHeight {
  // height: 108px;
}

.loaderContent {
  border-radius: 0px 0px 10px 10px;
  margin: 0px auto;
  max-width: 800px;
  position: absolute;
  inset: 0;
  z-index: 10;
}

.bgImg {
  flex: 1;
  height: 100%;
  display: inline-flex;
  background-color: transparent;
  height: auto;
  left: 0px;
  mix-blend-mode: normal;
  object-fit: cover;
  top: 0px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  position: relative;
}

.routerContainer {
  position: absolute;
  inset: 0;
}

.routerContent {
  width: 100%;
  height: 100%;
  background-color: #0f0f14;
  // border-radius: 20px;
  overflow: hidden;
  position: relative;
  left: 0px;
  transition: left var(--animation-duration) linear;
}

.loaderRunning {
  opacity: 0 !important;
}

.blurBg {
  border-radius: 0px;
  background-color: rgba(0, 0, 0, 0.5);
}

.appBg {
  // border-radius: 27.3px;
  background-repeat: no-repeat;
  background-size: contain;
  height: 100%;
  width: 100%;
}

.bgEllips {
  position: absolute;
  z-index: 1;
  top: -37px;
  left: 25px;
  width: 225px;
  height: 55px;
  filter: blur(55px);
  background-image: conic-gradient(from 0.32turn, var(--W--01), rgba(151, 134, 255, 0.59) 0.99turn, var(--W--01) 0.09turn, rgba(151, 134, 255, 0.1) 0.53turn, var(--W--01) 0.8turn, rgba(151, 134, 255, 0) 0.31turn, var(--W--01)), linear-gradient(348deg, var(--neutral-light) 210%, var(--neutral-light-transparent) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0), var(--secondary-color));
}

.bgEllips1 {
  position: absolute;
  z-index: 1;
  bottom: -26px;
  right: 23px;
  width: 78px;
  height: 32.5px;
  filter: blur(26px);
  background-image: conic-gradient(from 0.42turn, var(--W--01), rgba(151, 134, 255, 0.37) 0.89turn, var(--W--01) 0.09turn, rgba(151, 134, 255, 0.02) 0.83turn, var(--W--01) 0.51turn, var(--tertiary-color) 0.36turn, var(--W--01)), linear-gradient(274deg, var(--neutral-light) 210%, var(--neutral-light-transparent) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0.19), var(--secondary-color));
}

.orderSummaryContainer {
  .bgEllips {
    width: 170px;
    height: 45px;
  }
}


.toggle-switch {
  position: absolute;
  display: inline-block;
  width: 50px;
  height: 25px;
  top: 12px;
  right: 25px;
  z-index: 99;
}

.toggle-switch input {
  display: none;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 25px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 21px;
  width: 21px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #2196F3;
}

input:checked+.slider:before {
  transform: translateX(25px);
}

.WrapperContent {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 100%;
  justify-content: center;
  gap: 1.11%;
}

.RightContent {
  display: flex;
  flex-direction: column;
  width: 22.92%;
  padding-right: 1.11%;
  height: calc(100vh - 8.59vh);
}

.acrylicCorner {
  position: relative;
  top: 27px;
  opacity: 0.5;
}

.increaseWidth {
  width: 1151px;
}

.widthAnimation {
  transition: width var(--animation-duration) linear;
}

.moveRight {
  left: 351px;
}

.closeAndMinimizeBtn {
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 8px;
  right: 0px;
  z-index: 999999;
}

.macCloseAndMinimizeBtn {
  left: 5px;
  right: unset;
  flex-direction: row;
}

.hideCloseAndMinimizeBtn {
  display: none;
}

.wrapperMenuBorder {
  border-bottom-left-radius: 0px;
  border-top-left-radius: 0px;
}

.fillBorderRightBottom {
  border-bottom-right-radius: 0px;
}

.fillBorderRightTop {
  border-top-right-radius: 0px;
}

.positionCloseAndMinimizeBtn {
  left: 356px;
}

.referenceDataLoader {
  display: flex;
  column-gap: 16px;
  z-index: 9999;

  .fetchDataLoader {
    font-family: Inter;
    font-size: 16px;
    color: #fff;
    min-width: 200px;
    position: relative;

    &::after {
      content: '...'; // fixed content
      display: inline-block;
      width: 1.5em; // enough space for 3 dots
      text-align: left;
      margin-left: 2px;
      animation: stableDots 1s steps(4, end) infinite;
    }
  }

  @keyframes stableDots {
    0% {
      content: '.';
    }

    25% {
      content: '..';
    }

    50% {
      content: '...';
    }

    75% {
      content: '';
    }

    100% {
      content: '.';
    }
  }
}

.appFullScreen {
  height: 100%;
  width: 100%;
  background-color: #0f0f14;
  transition: height 1s linear;
  display: flex;
  flex-direction: column;
}

.mainContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  overflow: hidden;
  position: relative;
  height: 100%
}

.macTitleBar {
  width: 100%;
  height: 32px;
  padding: 2px 1384px 2px 8px;
  background-color: #191a20;
  display: flex;
  -webkit-app-region: drag;
  .macTitleBarLeft {
    display: flex;
    align-items: center;
  }
}
.flexSpace {
    flex: 1;
    width: auto;
}

.routeTab {
    height: 100%;
    background-color: #222329;
}

.mainViewWindow{
    position: relative;
    display: flex;
    flex-direction: row;
    width: 100%;
    height: calc(100% - 8.59vh);
}

.wrapperContentOverlay{
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.5);
}

 // Scroll to Bottom Indicator
        .scrollToBottomIndicator {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 11px 20px;
            background-color: #3b3c41;
            padding: 16px 20px;
            border-radius: 0 0 8px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 60px;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #3a3b42;
            }

            .scrollText {
                font-family: Inter;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: center;
                color: #c3c4ca;
            }

            .scrollArrows {
                display: flex;
                align-items: center;
            }

           
        }

        @keyframes bounce {0%,20%,50%,80%,100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-3px);
            }

            60% {
                transform: translateY(-2px);
            }
        }

        .videoPlayerRightWindowPopOutContainer{
          .subTitleStyle,.videoPlayerRightWindowContainer{
            height: 275px;
          }
          .videoPlayerRightWindowContainer{
              border-radius: 20px 20px 0px 0px;
              img{
                 border-radius: 20px 20px 0px 0px;
              }
          }
          .controlsRightWindow{
            border-radius: 20px 20px 0px 0px;
          }
          .videoPlayerDescriptionBottom{
            padding: 16px 12px;
            h2{
                font-family: Syncopate;
                font-size: 16px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 1.12px;
                text-align: left;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 12px;
                 display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            p{
              font-family: Inter;
              font-size: 14px;
              font-weight: 300;
              font-stretch: normal;
              font-style: normal;
              line-height: 1.3;
              letter-spacing: normal;
              text-align: left;
              color: rgba(255, 255, 255, 0.8);
               display: -webkit-box;
              -webkit-line-clamp: 3; 
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }

.formInputGroup1 {
  display: flex;
  gap: 20px;
}