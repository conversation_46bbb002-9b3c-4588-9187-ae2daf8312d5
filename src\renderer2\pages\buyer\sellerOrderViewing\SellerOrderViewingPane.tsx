import { memo, useEffect, useRef, useState } from 'react'
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import styles from './SellerOrderViewingPane.module.scss';
import SellerOrderViewingActionWindow from './SellerOrderViewingActionWindow';
import { useSellerOrderStore } from '@bryzos/giss-ui-library';
import AcceptOrder from '../../seller/acceptOrder';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../../assets/New-images/closePopup.svg';
import clsx from 'clsx';

const ErrorDialog = memo((props:any) => {
  return (
      <Dialog
          open={props.open}
          onClose={props.onClose}
          transitionDuration={200}
          hideBackdrop
          container={props.dialogRef}
          style={{
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backdropFilter: 'blur(7px)',
            WebkitBackdropFilter: 'blur(7px)',
            backgroundColor: 'rgba(0, 0, 0, 0.23)',
            zIndex: 9999
          }}
          PaperProps={{
            style: {
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                margin: 0,
                width: '100%'
            }
        }}
          classes={{
              root: styles.justMissedPopup,
              paper: styles.dialogContent
          }}

      >
          <span className={styles.closeIcon} onClick={props.onClose}><CloseIcon /></span>
          <p className={styles.youJustMissetext} >YOU JUST MISSED IT!</p>
          <p className={clsx(styles.thisOrderMissedtest,styles.thisOrderMissedtest1)}>This order was just claimed by another supplier.</p>
          <p className={styles.thisOrderMissedtest}>Continue to review more orders & click “Accept Order”<br/>as soon as you know you can fulfill the order. Good luck!</p>
          <button className={styles.claimAnotherOrderbtn} onClick={($event: any) => props.onClose($event)}>Claim Another Order</button>
      </Dialog>
  )
})

const SellerOrderViewingPane = ({containerRef}: {containerRef: any}) => {
  const { setLoadComponent } = useRightWindowStore();
  const { orderToBeShownInOrderAccept } = useSellerOrderStore() as any;
  const setOrderDetail = useSellerOrderStore((state: any) => state.setOrderToBeShownInOrderAccept);
  const setErrorPopupDetail = useSellerOrderStore((state: any) => state.setErrorPopupDetail);
  const [openErrorPopUp, setOpenErrorPopUp] = useState(false);
  const errorPopupDetail = useSellerOrderStore((state: any) => state.errorPopupDetail);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    setLoadComponent(<SellerOrderViewingActionWindow />);
    return () => {
      setLoadComponent(null);
      setOrderDetail({});
      setErrorPopupDetail(false);
    }
  }, [location.pathname]);

  useEffect(() => {
    if(!(orderToBeShownInOrderAccept && Object.keys(orderToBeShownInOrderAccept).length > 0)){
      setLoadComponent(<SellerOrderViewingActionWindow />);
    }
  }, [orderToBeShownInOrderAccept])

  useEffect(() => {
      if (errorPopupDetail) {
          setErrorMessage(errorPopupDetail);
          setOpenErrorPopUp(true);
      }
  }, [errorPopupDetail]);

  const handleErrorClose = ($event: any) => {
      $event.stopPropagation();
      setOpenErrorPopUp(false);
      setErrorPopupDetail(null);
      setOrderDetail({})
  };

  return (
    <>
      {
        (orderToBeShownInOrderAccept && Object.keys(orderToBeShownInOrderAccept).length > 0) ? (
            <AcceptOrder key={orderToBeShownInOrderAccept.id} containerRef={containerRef}/>
        ) : (
          <div className={styles.viewingPane}>
            <div className={styles.placeholderContent}>
              <p className={styles.placeholderText}>
                This is the viewing pane.
              </p>
              <p className={styles.placeholderText}>
                Select from the list on the left to
              </p>
              <p className={styles.placeholderText}>
                review an order.
              </p>
            </div>
          </div>

        )
      }
      <ErrorDialog
          open={openErrorPopUp}
          onClose={($event:any) => handleErrorClose($event)}
          errorMessage={errorMessage}
          dialogRef={containerRef.current}
      />
    </>
  )
}

export default SellerOrderViewingPane;