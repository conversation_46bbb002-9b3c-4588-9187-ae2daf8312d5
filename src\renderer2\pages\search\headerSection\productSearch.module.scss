  .searchSection {
    padding: 0px 16px;
    display: flex;

    &[data-disabled="true"] {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .searchBox {
      width: 100%;
      height: 45px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 16px;
      border-radius: 13.3px;
      background: rgba(255, 255, 255, 0.04);
      transition: background-color 0.1s ease-in-out;
      z-index: 2;
    
      &.firstTimeFocusBg {
        background-image: linear-gradient(104deg, #1c40e7 -1%, #16b9ff 102%);
        svg {
          path {
            fill: #fff;
          }
        }
        input {
          &::placeholder {
            color: #fff;
          }
        }
      }

      &.searchInputStartTyping {
        // background: url('../../../assets/New-images/New-Image-latest/seach-input-new-frame.svg') no-repeat bottom;
        // background-size: cover;
        position: relative;
        border-radius: 13.3px;
        overflow: hidden;
        z-index: 0;
         box-shadow: inset 3px 3px 7.4px 0 #000;
        &::before {
          content: '';
          position: absolute;
          inset: 0;
          border-radius: inherit;
          padding: 1.5px;
          background: linear-gradient(to bottom right, #1a1b20 54%, #fff 294%);
          -webkit-mask:
            linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          background-clip: border-box;
          z-index: -1;
          pointer-events: none;
          left: -2px;
          top: -3px;
        }

      }
    
      input {
        background-color: transparent;
        border: none;
        width: 100%;
        height: 100%;
        padding: 6px 12px 6px 0;
        font-family: Inter;
        font-size: 15px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.6px;
        text-align: left;
        color: #fff;
        transition: all 0.1s ease-in-out;
    
        &::placeholder {
          color: #616575;
        }
    
        &:focus {
          outline: none;
        }

        &:disabled {
            cursor: not-allowed;
          }
      }
    
      svg {
        margin-left: 18px;
      }
    }
    
  }

  .searchFilterSection {
    display: flex;
    padding: 16px 2.16%;
    justify-content: space-between;
    .zipBox {
      display: flex;
      flex-direction: column;
      min-width: 22.67%;

      .destZIP {
        background-color: #2b2c32;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.48px;
        text-align: left;
        color: #9b9eac;
        padding: 7px 0px 8px 12px;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
      }
      .zipCode {
        input {
          background: #303136;
          height: 34px;
          border: none;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: 0.56px;
          text-align: left;
          color: #fff;
          padding-left: 12px;
          border-bottom-right-radius: 10px;
          border-bottom-left-radius: 10px;
          width: 100%;
          &:focus{
            outline: none;
          }
        }
      }
    }
    .dropdownBox {
      display: flex;
      flex-direction: column;
      min-width: 22.67%;
      .dropdownLabel {
        font-family: Inter;
        font-size: 12px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.48px;
        text-align: left;
        color: #9b9eac;
        padding: 7px 0px 8px 12px;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
        background-color: #2b2c32;
      }
      .dropdownValue {
          background: #303136;
          height: 34px;
          border: none;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: 0.56px;
          text-align: left;
          color: #fff;
          padding-left: 12px;
          border-radius: 0px 0px 10px 10px;
      }
    }
  }
  .dropDownBG.dropDownBG {
    width: 110px;
    z-index: 999;
    padding: 4px;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #9c9da5;
    
        ul {
            padding: 0px;
    
            li {
            font-family: Inter;
            font-size: 14px;
            font-weight: 500;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: #191a20;
            margin-bottom: 2px;
            &[aria-selected="true"] {
                border-radius: 6px;
                background-color: #e0e0e0;
                font-weight: bold;
            }
            &:hover {
                border-radius: 6px;
                background-color: #e0e0e0;
                font-weight: bold;
            }
            }
        }
    }


.savedSearchExpired {
  display: flex;
  gap: 20px;
  padding: 16px 16px 0px 16px;
  width: 100%;

  .savedSearchExpiredDetails {
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 16px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    display: flex;
    flex-direction: column;
    gap: 23px;
    width: 71.77%;

    .savedSearchExpiredDetailsSpan {
      color: #fff;
    }
  }

  .savedSearchExpiredDetails1 {
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .savedSearchExpiredDetailsSpan1 {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #ff7759;
      padding-bottom: 11px;
    }

    .lastCreatedDate {
      font-family: Inter;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.48px;
      text-align: center;
      color: #fff;
    }

    .lastCreatedDateSpan {
      display: flex;
      font-family: Inter;
      font-size: 12px;
      font-weight: 200;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.48px;
      text-align: center;
      color: #fff;
      padding: 0px 16px;
    }
  }
}