import { commomKeys, emojiRemoverRegex, formatToTwoDecimalPlaces, noIdGeneric, priceUnits, useBuyerSettingStore, useCreatePoStore, useGlobalStore , getChannelWindow, format4DigitAmount, orderType, formatCurrencyWithComma } from "@bryzos/giss-ui-library";
import { calculateLineWeight, calculateTotalPurchase, clearLocal, convertUtcToLocalTime, exportToExcel, fetchPrice, formatAndUpdateDraftPoData, formatDisplayDateForTemplate, getLocal, handleDraftPoSave, setLocal, transformOrderToSheetData } from "src/renderer2/helper";
import { useLeftPanelStore } from "../LeftPanelStore";
import styles from "../ListTab/ListTab.module.scss";
import { ReactComponent as DeleteIcon } from "../../../assets/New-images/New-Image-latest/delete-outlined.svg";
import { ReactComponent as ShareIcon } from "../../../assets/New-images/New-Image-latest/share-outlined.svg";
import { ReactComponent as EditIcon } from "../../../assets/New-images/New-Image-latest/pencil-outlined.svg";
import { ReactComponent as ResetIcon } from "../../../assets/New-images/New-Image-latest/icon-reset.svg";
import { ReactComponent as Purchasing } from "../../../assets/New-images/New-Image-latest/search-result/purchasing.svg";
import { ReactComponent as Quote } from "../../../assets/New-images/New-Image-latest/search-result/quoting.svg";
import { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import clsx from "clsx";
import useDialogStore from "../../DialogPopup/DialogStore";
import useDeleteSearchProducts from "src/renderer2/hooks/useDeleteSearchProducts";
import { localStorageKeys, routes, shareEmailTypes } from "src/renderer2/common";
import ShareEmailWindow from "../../ShareEmailWindow/ShareEmailWindow";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import useGetDraftLines from "src/renderer2/hooks/useGetDraftLines";
import usePostCancelDraftPo from "src/renderer2/hooks/usePostCancelDraftPo";
import { useRightWindowStore } from "src/renderer2/pages/RightWindow/RightWindowStore";
import { Popover } from '@mui/material';
import { descriptionLines, getOtherDescriptionLines } from "src/renderer2/utility/pdfUtils";
import PdfMakePage from "src/renderer2/pages/PdfMake/pdfMake";
import { useLocation, useNavigate } from "react-router-dom";
import {useDeleteItem} from '../../../store/ItemManagerStore'
import { useRestoreDeletedItem } from "src/renderer2/hooks/useRestoreDeletedItems";
import useGetDeletedItemsLines from "src/renderer2/hooks/useGetDeletedItemsLines";
import usePostDraftPo from "src/renderer2/hooks/usePostDraftPo";
import { useQueryClient } from "@tanstack/react-query";
dayjs.extend(customParseFormat);

interface DraftPoTemplateProps {
    item: any;
    index: number;
    isSearchMode?: boolean;
    animatedItems: Set<string>;
    selectedSavedSearchIdList: any[];
    setSelectedSavedSearchIdList: (ids: any[]) => void;
    lastClickedIndex: number | null;
    setLastClickedIndex: (index: number | null) => void;
    handleCtrlClick: (item: any, index: number, onCtrlClickCallback: (currentSelectedIds: any[], updatedIds: any[]) => void) => void;
}

const DraftPoTemplate = forwardRef<HTMLDivElement, DraftPoTemplateProps>(({item, index, animatedItems, selectedSavedSearchIdList, setSelectedSavedSearchIdList, lastClickedIndex, setLastClickedIndex, handleCtrlClick, isSearchMode = false, ...props}: DraftPoTemplateProps, ref) => {
    const location = useLocation();
    const isBuyerDeletedItemsPage =
      location.pathname === routes.buyerDeleteOrderPage;
    const deleteItem = useDeleteItem();
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
    const setCreatePoData = useCreatePoStore(state => state.setCreatePoData);
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const productMapping = useGlobalStore(state => state.productMapping);
    const setIsCreatePoDirty = useCreatePoStore(state => state.setIsCreatePoDirty);
    const draftOrderListFromSocket = useCreatePoStore((state: any) => state.draftOrderListFromSocket);
    const setDraftOrderListFromSocket = useCreatePoStore((state: any) => state.setDraftOrderListFromSocket);
    const updatedDraftId = useCreatePoStore((state: any) => state.updatedDraftId);
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const {mutateAsync: getDraftLines} = useGetDraftLines();
    const {mutateAsync: getDeletedItemsLines} = useGetDeletedItemsLines();
    const {mutateAsync: cancelDraftPo} = usePostCancelDraftPo();
    const [shareAnchorEl, setShareAnchorEl] = useState<HTMLButtonElement | null>(null);
    const { setShareEmailWindowProps, setLoadComponent, setShareEmailType } = useRightWindowStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const quoteList = useCreatePoStore(state => state.quoteList);
    const buyerSettings = useBuyerSettingStore(state => state.buyerSetting);
    const channelWindow = getChannelWindow();
    const setQuoteList = useCreatePoStore(state => state.setQuoteList);
    const setPurchasingList = useCreatePoStore(state => state.setPurchasingList);
    const purchasingList = useCreatePoStore(state => state.purchasingList);
    const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
    const navigate = useNavigate();
    const postDraftPo = usePostDraftPo();
    const queryClient = useQueryClient();
    const totalPurchase = useMemo(() => {
        return calculateTotalPurchase(item).toFixed(2);
      }, [item]);
      const { restoredItem, handleRestoreDeletedItem, isLoading } =
      useRestoreDeletedItem();

    const isShippingDetailsFilled = useMemo(()=>{
        return !!item.shipping_details?.city && !!item.shipping_details?.state_id && !!item.shipping_details?.zip && !!item?.delivery_date && !!item?.buyer_internal_po && !!item.shipping_details?.line1;
    },[item]);


    const disableExportToPdf = useMemo(() => {
        return !selectedQuote?.cart_items?.length || !buyerSettings?.delivery_address || buyerSettings?.delivery_address?.length === 0 || !isShippingDetailsFilled;
    }, [selectedQuote?.cart_items, buyerSettings, isShippingDetailsFilled]);

    const isSharePopoverOpen = Boolean(shareAnchorEl && selectedQuote?.id === item.id);

    useEffect(()=>{
        if(item)
            if(item?.id?.includes(noIdGeneric) && !animatedItems.has(item.id)){
                setTimeout(()=>{
                    animatedItems.add(item.id);
                },3000);
            }
    },[item]);

    
    // useEffect(()=>{
    //     const getLocalQuote = getLocal(localStorageKeys.poQuoting, null);
    //     if(draftOrderListFromSocket?.length > 0 && selectedQuote?.id){
    //         const selectedQuoteFetchLatestData = draftOrderListFromSocket.find((item: any) => item.id === selectedQuote?.id);
    //         if(!selectedQuoteFetchLatestData) setDraftOrderListFromSocket([]);
    //         if(selectedQuoteFetchLatestData?.id && selectedQuoteFetchLatestData?.id === selectedQuote?.id){
    //             setDraftOrderListFromSocket([]);
    //             handleUpdateSelectedData(selectedQuoteFetchLatestData);
    //         }
    //     }
    // },[draftOrderListFromSocket])

    const handleUpdateSelectedData = async(selectedQuoteFetchLatestData: any) => {
        if(!selectedQuote.pricing_expired){
            showCommonDialog(null, 'This quote has been updated.', null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        }
        clearLocal(localStorageKeys.poQuoting);
        setUpdatedDraftId(null);
        await handleLoadQuoteData(selectedQuoteFetchLatestData);

    }

    const handleItemClick = async (item: any, index: number, event: React.MouseEvent | undefined) => {
        try{
            if(location.pathname === routes.orderConfirmationPage){
                const routeName = item.order_type === orderType.QUOTE ? routes.quotePage : routes.createPoPage;
                navigate(routeName);
            }
            // Handle shift-click for multiple selection
            if (event?.shiftKey && lastClickedIndex !== null) {
                handleCtrlClick(item, index, handleWhenCtrlClickOnItem);
                return;
            }
            if(selectedQuote?.id === item.id){
                return;
            }
            setShareEmailWindowProps(null);
            setShareEmailType(null);
            setLastClickedIndex(index);
            setSelectedSavedSearchIdList([item.id]); // Reset to single selection
            await handleLoadQuoteData(item);
        } catch(error){
            setShowLoader(false);
            console.log("error @>>>>>>>", error);
        }
    }

    const handleWhenCtrlClickOnItem = (currentSelectedIds: any[], updatedIds: any[]) => {
        const itemId = item.id;
        if (currentSelectedIds.includes(itemId)) {
            if (selectedQuote?.id === itemId) {
                // clearSelectedSavedSearch();
                // const itemIndex = savedSearchProducts.findIndex((item: any) => item.id === updatedIds[0]);
                if (item.id === updatedIds[0]) {
                    handleLoadQuoteData(item);
                }
            }
        }
        if (updatedIds.length === 1 && selectedQuote?.id !== updatedIds[0]) {
            handleItemClick(item, index, undefined);
            return;
        }
    }

    const handleLoadQuoteData = async (item: any) => {
        setShowLoader(true);
        setIsCreatePoDirty(false);
        const draftLines = isBuyerDeletedItemsPage ? await getDeletedItemsLines(item.id) : await getDraftLines(item.id);
        formatAndUpdateDraftPoData(item, draftLines?.data);
        // setShowLoader(false);
    }

    const handleEditClick = (item: any, e: React.MouseEvent) => {
        e.stopPropagation();
        if(selectedQuote?.id === item.id){
            const updateSelectedQuote = {...selectedQuote, isEdit: true};
            setSelectedQuote(updateSelectedQuote);
            setCreatePoData(updateSelectedQuote);
        }
    }

    const handleDeleteClick = async (e: React.MouseEvent, item: any) => {
        e.stopPropagation();
        if(item?.id.includes(noIdGeneric)){
            const draftId = await saveLocalChanges()
            if(draftId){
                item.id = draftId
            }
        }
        try{
            const payload = {
                data: [item.id]
            }
            setSelectedQuote(null);
            const response = await cancelDraftPo(payload);
            if(response?.data?.data){
                setSelectedQuote(null);
                setSelectedSavedSearchIdList([]);
                setLastClickedIndex(null);
                deleteItem(item, item.order_type);
                setClickedCreateNewButton(Math.random());
            }
        } catch(error){
            console.log("error @>>>>>>>", error);
            await handleLoadQuoteData(item);
        }
    }

    const getExportPoData = () => {
        let _recevingHours = [];
        if(buyerSettings?.delivery_address?.length === 1){
            _recevingHours = buyerSettings?.delivery_address[0]?.user_delivery_receiving_availability_details;
        }else{
            if(selectedQuote?.shipping_details?.delivery_address_id || item?.shipping_details?.delivery_address_id){
                const deliveryAddress = buyerSettings?.delivery_address?.find((address: any) => address.id === (selectedQuote?.shipping_details?.delivery_address_id || item?.shipping_details?.delivery_address_id));
                _recevingHours = deliveryAddress?.user_delivery_receiving_availability_details;
            }else{
                _recevingHours = buyerSettings?.delivery_address?.find((address: any) => address.is_default)?.user_delivery_receiving_availability_details;
            }
        }
    

        const formmattedItem = {...item, totalPurchase : totalPurchase}
        return {...formmattedItem , recevingHours : _recevingHours}
    }

    const getCartItems = () =>{
        const {cart_items} = selectedQuote;
        const formattedItems = cart_items.filter(item => ((item?.descriptionObj?.UI_Description && !!item?.qty) || item?.lineStatus)).map((item, index) => ({
            description: descriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            otherDescription: getOtherDescriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            product_tag: item?.product_tag ?? '',
            domesticMaterialOnly: item?.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item?.qty ?? ''),
            qty_unit: item?.qty_unit?.toLowerCase() ?? '',
            price_unit: item?.price_unit?.toLowerCase() ?? '',
            extended: formatToTwoDecimalPlaces(item?.extendedValue ?? ''),
            price: item?.price_unit?.toLowerCase() === priceUnits.lb ? format4DigitAmount(item?.buyer_price_per_unit ?? '') : formatToTwoDecimalPlaces(item?.buyer_price_per_unit ?? ''),
            line_weight: location.pathname === routes.savedBom ? formatToTwoDecimalPlaces(item?.line_weight ?? '') : calculateLineWeight(item),
            line_weight_unit: "Lb", 
            line_no: index,
            po_line: index.toString(),
            descriptionObj:item?.descriptionObj ?? {},
            extendedValue:item?.extendedValue ?? '',
            lineStatus: item?.line_status ?? ''
        }));
        return formattedItems
    }

    const handleExportToExcel = (e: React.MouseEvent) => {
        e.stopPropagation();
        setShareAnchorEl(null);
        
        exportToExcel(transformOrderToSheetData(selectedQuote), selectedQuote.buyer_internal_po);
    }

    const handleSharePriceClick = async (e: React.MouseEvent<HTMLButtonElement>, item: any) => {
        e.stopPropagation();
        setShareAnchorEl(e.currentTarget);
        if(selectedQuote?.id === item.id) return;
        await handleLoadQuoteData(item);
    }

    const handleCloseSharePopover = (e) => {
        e.stopPropagation();
        setShareAnchorEl(null);
    }

    const handleSharePrice = async (e: React.MouseEvent, item: any) => {
        e.stopPropagation();
        setSelectedSavedSearchIdList([]);
        setShareEmailWindowProps({ isSharePrice: true });
        if(item?.id.includes(noIdGeneric)){
            saveLocalChanges()
        }
        // setLoadComponent(<ShareEmailWindow />)
        setShareEmailType(shareEmailTypes.shareQuote);
        setShareAnchorEl(null);
    }
    
    let animate = false;
    if(item?.id?.includes(noIdGeneric) && !animatedItems.has(item.id)) {
        animate = true;
    }

    const saveLocalChanges = async () => {
        // Set flag to indicate saving is in progress
        try {
            const localQuote = getLocal(localStorageKeys.poQuoting, null);
            const localPurchasing = getLocal(localStorageKeys.poPurchasing, null);
            const localDraftData = localQuote || localPurchasing;
            if (localDraftData) {
                const id = await handleDraftPoSave(localDraftData, postDraftPo , queryClient);
                setIsCreatePoDirty(false);
                setSelectedQuote({...selectedQuote, id: id});
                return id;
            }
        } catch (error) {
            console.error('Error in saveLocalChanges:', error);
        }
    }




    return (
        <div
            ref={ref}
            key={index}
            className={clsx(styles.searchItemContainer, (selectedQuote?.id === item.id || selectedSavedSearchIdList.includes(item.id)) && styles.selectedSearchItem)}
            onClick={(e) => { handleItemClick(item, index, e); }}>
            <div className={styles.searchTitle}>
                <span className={clsx(styles.searchTitleText, {
                    [styles.initialPositionForAnimation]: animate,
                    [styles.slideInAnimation1]: animate
                })}>
                    {item.buyer_internal_po}
                    {/* {((selectedQuote?.id !== item.id) || (selectedQuote?.id === item.id && !selectedQuote?.isEdit)) &&
                    <span className={styles.editIcon} onClick={(e) => handleEditClick(item, e)}><EditIcon /></span>
                } */}
                </span>
                <span className={styles.itemCount}>{isSearchMode && <span> {item.order_type === orderType.QUOTE ? <Quote /> : <Purchasing />} </span>}${formatToTwoDecimalPlaces(totalPurchase)}</span>
                <div className={styles.iconContainer}>
                   {!isBuyerDeletedItemsPage ? <> {/* <span className={styles.shareIcon} onClick={(e) => console.log("share icon clicked ", e, item)}></span> */}
                    <div className={styles.exportContainer}>
                        <button
                            className={clsx(styles.selectedProductHeaderButton, styles.shareIcon)}
                            onClick={(e) => handleSharePriceClick(e, item)}
                            
                        >
                            <ShareIcon />
                        </button>
                        <Popover
                            open={isSharePopoverOpen}
                            anchorEl={shareAnchorEl}
                            onClose={handleCloseSharePopover}
                            anchorOrigin={{
                                vertical: 'top',
                                horizontal: 'center',
                            }}
                            transformOrigin={{
                                vertical: 'bottom',
                                horizontal: 86,
                            }}
                            classes={{
                                paper: styles.exportDropdownMenu,
                            }}
                        >
                            <div>
                                {/* <button
                                    className={styles.exportOption}
                                    onClick={(e) => handleSharePrice(e, item)}
                                >
                                    Share
                                </button> */}
                                {(channelWindow?.fetchPdf || channelWindow?.generatePdf) &&  
                                        <PdfMakePage getExportPoData={getExportPoData} buyingPreferenceData={buyerSettings} disabled={disableExportToPdf} getCartItems={getCartItems} />
                                    }
                                <button
                                    className={styles.exportOption}
                                    onClick={handleExportToExcel}
                                >
                                    Export to Excel
                                </button>
                            </div>
                        </Popover>
                    </div>
                    <span className={styles.deleteIcon} onClick={(e) => handleDeleteClick(e, item)}><DeleteIcon /></span></> : <>
                    <span className={styles.deleteIcon} onClick={(e) => {
                      e.stopPropagation();
                      handleRestoreDeletedItem(item)
                    }}><ResetIcon /></span></>}
                </div>
            </div>
            <div className={styles.searchDetails}>
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation2]: animate
                    })}>{item.shipping_details?.city}{(item.shipping_details?.city && item.shipping_details?.state_code) && ','} {item.shipping_details?.state_code}
                    </span>
                </div>
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation3]: animate
                    })}>{formatCurrencyWithComma(Math.floor(item.total_weight || 0).toString())} LBS</span>
                </div>
                {/* {item?.products?.length > 0 ?
                Array.from(new Set(
                    item?.products
                        .map((obj: any) => productMapping[obj.product_id]?.Key2 ?? '')
                )).join(', ')
            : '-'} */}
                {/* {orderSizeData ? <span>Based Upon {Number(orderSizeData?.min_weight) === Number(orderSizeList[orderSizeList.length - 1].min_weight) ? Number(orderSizeData?.min_weight).toLocaleString() + '+' : `${Number(orderSizeData?.min_weight).toLocaleString()} to ${Number(orderSizeData?.max_weight).toLocaleString()}`} LBS</span> : '-'}<br /> */}
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation4]: animate
                    })}>{item?.created_date ? formatDisplayDateForTemplate(convertUtcToLocalTime(item.created_date)) : '-'}</span>
                </div>
            </div>

        </div>
    )
});

export default DraftPoTemplate