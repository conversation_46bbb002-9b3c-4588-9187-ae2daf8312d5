.headerInfoContainer {
    padding: 16px;
    position: relative;
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    background-color: #191a20;
}

.calendarOpenOverlay {
    width: 100%;
    height: 100%;
    pointer-events: none;
    -webkit-backdrop-filter: blur(8.9px);
    backdrop-filter: blur(8.9px);
    background-color: rgba(76, 76, 76, 0.09);
    position: absolute;
    z-index: 999;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
}
.buyerCancellationContainer{
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.08);
    padding:16px 12px;
    display: flex;
    flex-direction: column;
    h2{
        font-family: Inter;
        font-size: 20px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: normal;
        text-align: left;
        color: #fff;
        margin-bottom: 8px;
    }
    .buyerCancellationDescription{
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.56px;
        text-align: left;
        color: rgba(255, 255, 255, 0.5);
    }
    .buyerCancellationButtons{
        padding-top: 16px;
        display: flex;
        gap: 12px;
        margin-left: auto;
        button{
            padding: 8px 16px;
            border-radius: 500px;
            background-color: rgba(255, 255, 255, 0.08);
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: center;
            color: #9b9eac;
            &:hover{
                color: #fff;
            }
        }
    }
    .acceptCancellationContainer{
        padding-top: 16px;
        display: flex;
        gap: 12px;
        .acceptCancellationInput{
            display: flex;
            flex-direction: row;
            gap: 12px;
            height: 36px;
            text-align: center;
            align-items: center;
            label {
                font-family: Inter;
                font-size: 14px;
                font-weight: 600;
                font-stretch: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: 0.56px;
                text-align: center;
                color: #fff;
              }
        }
        .buyerCancellationButtons{
            padding-top: 0px;
            margin-right: auto;
            margin-left: unset;
        }
    }
}

.isCalendarOpenDiabledInput {

    // background-image: linear-gradient(79deg, #0f0f14 50%, #393e47 135%);
    input,
    .deliverToContainer,
    .addressInputs,
    .uploadBillContainer,
    .radioGroupContainer,
    .deliverByButton1,
    .deliverByButton2 {
        pointer-events: none;
    }
}


.createPoHeaderInfoGrid {
    display: flex;
    justify-content: space-between;
    column-gap: 12px;


    .leftGridHeader {
        width: 40%;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .col1 {
            width: 100%;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            display: flex;
            gap: 7px;
            align-items: center;

            .poNameLabel {
                 color: rgba(255, 255, 255, 0.5);
                // width: 89px;
                display: flex;
                justify-content: space-between;
                white-space: nowrap;

            }

            .pOInputValue {
                display: flex;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 0.56px;
                text-align: left;
                color: #fff;
            }

            &.deliverByContainer {
                width: 100%;
                position: relative;

                button {
                    height: 20px;
                    font-family: Inter;
                    font-size: 12px;
                    border-radius: 4px;

                    span {
                        height: 100%;
                        border-radius: 0px;
                    }
                }
            }
        }

    }

    .rightGridHeader {
        width: 60%;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 16px;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        row-gap: 12px;

        .poNameLabel {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: rgba(255, 255, 255, 0.5);
            span{
                color: #fff;
                text-transform: uppercase;
                margin-left: 7px;
            }
            &:nth-child(2){
               text-transform: uppercase;
            }
        }
    }


}

.disputeHeaderInfoGrid {
    display: flex;
    justify-content: space-between;
    column-gap: 12px;


    .leftGridHeader {
        min-width: 55%;
        border-radius: 12px;
        padding: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .col1 {
            width: 100%;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            display: flex;
            gap: 7px;
            align-items: center;

            .poNameLabel {
                 color: rgba(255, 255, 255, 0.5);
                // width: 89px;
                display: flex;
                justify-content: space-between;
                white-space: nowrap;

            }

            .pOInputValue {
                display: flex;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.2;
                letter-spacing: 0.56px;
                text-align: left;
                color: #fff;
                flex-direction: column;
                width: 100%;
                gap: 8px;
                .disputeItem{
                    display: flex;
                    gap: 37px;
                    label{
                        min-width: 80px;
                    }
                }
            }

            &.deliverByContainer {
                width: 100%;
                position: relative;

                button {
                    height: 20px;
                    font-family: Inter;
                    font-size: 12px;
                    border-radius: 4px;

                    span {
                        height: 100%;
                        border-radius: 0px;
                    }
                }
            }
        }

    }

    .rightGridHeader {
        width: 100%;
        border-radius: 12px;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: row;
        gap: 12px;
        position: relative;
        .calendarWrapper.calendarWrapper{
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
        }

        .poNameLabel {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: rgba(255, 255, 255, 0.5);
            span{
                color: #fff;
                text-transform: uppercase;
                margin-left: 7px;
            }
            &:nth-child(2){
               text-transform: uppercase;
            }
        }
        .actionButtonsContainer{
            display: flex;
            gap: 12px;
            .actionButtons{
                padding: 10px 38px;
                border-radius: 50px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255, 0.5);
                &:hover{
                    color: #fff;
                }
            }
        }
    }


}
.deliveryDateContainer {
    padding: 12px 16px 12px 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    position: relative;

    span {
        color: #fff;
        margin-left: 7px;
    }
}
.pointRight {
    position: absolute;
    top: 37px;
    left: -6px;
}

.poInputMain {
    position: relative;

    input {
        width: 100%;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 4px 8px;
        height: 20px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
        flex: 1;
    }

    .hasValue {
        position: relative;

        &:focus-within {
            overflow: hidden;
            z-index: 0;
            border-radius: 12px;

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                border-radius: inherit;
                padding: 1px;
                background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);

                -webkit-mask:
                    linear-gradient(#fff 0 0) content-box,
                    linear-gradient(#fff 0 0);
                -webkit-mask-composite: xor;
                mask-composite: exclude;

                background-clip: border-box;
                z-index: -1;
                pointer-events: none;
            }


        }

        .pOInput {
            &:focus {
                border: 0px solid transparent;
                background: transparent;
            }
        }
    }

}

.pOInput {
    border: 0px solid transparent;
    transition: all 0.2s ease;
    width: 100%;

    &:focus {
        border: 0px solid transparent;
        color: #fff;
        background-image: linear-gradient(126deg, #1c40e7 -20%, #16b9ff 114%), linear-gradient(286deg, #fff 116%, #1a1b20 30%);
        box-shadow: inset 3px 3px 7px rgba(0, 0, 0, 1);

        &::placeholder {
            color: #fff;
        }
    }

}


.deliverToContainer {
    width: 100%;
    height: 100px;
    padding: 4px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);

}

.autocompleteContainer {
    position: relative;
    width: 100%;

    :global(.MuiAutocomplete-root) {
        width: 100%;

        :global(.MuiAutocomplete-inputRoot) {
            -webkit-box-flex-wrap: nowrap;
            -ms-flex-wrap: nowrap;
            flex-wrap: nowrap;
        }

        .MuiAutocomplete-inputRoot.MuiAutocomplete-inputRoot {
            -webkit-box-flex-wrap: nowrap;
            -ms-flex-wrap: nowrap;
            flex-wrap: nowrap;
        }

        .MuiAutocomplete-noOptions.MuiAutocomplete-noOptions {
            display: none;

        }
    }
}

.muiAutocompleteTextField.muiAutocompleteTextField {
    width: 100%;
    height: 20px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);

    input {
        padding: 0px !important;
    }


    :global(.MuiInputBase-root) {
        height: 20px;
        width: 100%;
        border: 1px solid transparent;
        border-radius: 8px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
        padding: 0 8px;
        transition: all 0.1s ease;

        &:hover {
            border-color: transparent;
        }

        &.Mui-focused {
            border-color: transparent;
            color: #fff;
            box-shadow: none;
        }

        &.Mui-error {
            box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
            border: 0px;
            background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
            background-origin: border-box;
            background-clip: border-box, border-box;
            color: #fff;

            &.Mui-focused {
                color: #fff;
                box-shadow: none;
            }
        }
    }

    :global(.MuiInputBase-input) {
        height: 100%;
        padding: 0;
        color: inherit;
        font-family: inherit;
        font-size: inherit;
        letter-spacing: inherit;
        border: none;
        outline: none;
        background-color: transparent;
        caret-color: #1fbbff;

        &::placeholder {
            font-family: Syncopate;
            font-size: 12px;
            letter-spacing: 0.56px;
            color: #616575;
            opacity: 1;
        }

        &:focus {
            outline: none;
            color: #1fbbff;
        }

        &.Mui-error {
            color: #fff;

            &::placeholder {
                color: white;
            }
        }
    }

    :global(.MuiInputAdornment-root) {
        color: #616575;
    }

    :global(.MuiFormHelperText-root) {
        display: none;
    }

    :global(.MuiInputLabel-root) {
        display: none;
    }

    :global(.MuiOutlinedInput-notchedOutline) {
        border: none;
    }
}



.autocompleteDropdown.autocompleteDropdown {
    max-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    gap: 8px;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    background-color: rgba(128, 130, 140, 0.28);
    margin-top: 4px;

    ul {
        &::-webkit-scrollbar {
            width: 6px;
        }

        li {
            margin-right: 3px;
            padding: 6px 16px;
            border-radius: 8px;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            background-color: transparent;

            &:hover {
                background-color: rgba(255, 255, 255, 0.2);
                color: #fff;
            }

            &[aria-selected='true'] {
                background-color: transparent !important;
                box-shadow: unset;
                color: #fff;

                &:hover {
                    background-color: rgba(255, 255, 255, 0.2) !important;
                    color: #fff;
                }
            }
        }
    }

    &.autocompleteDBA {
        ul {
            li {
                font-size: 16px;
                padding: 8px 16px;
            }
        }
    }
}

.stateZipContainer {
    display: flex;
    gap: 4px;
    margin-top: 4px;

    span {
        &:nth-child(1) {
            flex: 0 45%;
        }

        &:nth-child(2) {
            flex: 0 55%;
        }
    }
}

.addressInputsCol2 {
    position: relative;

    input {
        width: 100%;
        height: 20px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 4px 8px 4px 8px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
        border: none;
    }

    .shape1 {
        width: 14px;
        height: 14px;
        position: absolute;
        transform: rotate(-450deg);
        top: -6px;
        left: -12px;

        svg {
            filter: opacity(0.28);
        }
    }

    .shape2 {
        width: 13px;
        height: 14px;
        position: absolute;
        transform: rotate(180deg);
        top: -1px;
        right: -5.8px;

        svg {
            filter: opacity(0.28);
        }

    }

    &.selectShade {
        background-color: #3e3f47;
        height: 26px;
        top: -6px;
        position: relative;
        border-radius: 0px 0px 8px 8px;
    }


    .stateWrapper {
        height: 34px;
        border-radius: 8px;
        background-color: #3e3f47;
        display: flex;
        align-items: center;
        padding: 4px 0px 0px 5px;

        svg {
            position: absolute;
            right: 2px;
            top: 8px;
        }

        input {
            width: 63px;
            height: 24px;
            padding: 5px 2.4px 5px 4.5px;
            border-radius: 6px;
            background-color: #111217;
            font-family: Syncopate;
            font-size: 14px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #459fff;

            &::placeholder {
                color: rgba(97, 101, 117, 0.5);
            }
        }
    }
}

.deliverToLabel {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
}

.addressInputs {
    width: 100%;
    height: 20px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 4px 8px 4px 8px;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.48px;
    text-align: left;
    color: #fff;
}

.lastAddressFiled1 {
    .addressInputsCol1 {
        width: 100%;
        height: 20px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        padding: 4px 8px 4px 8px;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
    }

    .stateZipContainer1 {
        display: flex;
        gap: 4px;
        margin-top: 4px;

        p {
            height: 20px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.04);
            padding: 4px 8px 4px 8px;
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 0.48px;
            text-align: left;
            color: #fff;

            &:nth-child(1) {
                flex: 0 45%;
            }

            &:nth-child(2) {
                flex: 0 55%;
            }
        }
    }
}

.addressInputsCol3 {
    input {
        width: 100%;
        height: 20px;
        padding: 4px 8px 4px 8px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.48px;
        text-align: left;
        color: #fff;
    }
}

.stateDropdown {
    .stateWrapper {
        input {
            border: none;
        }
    }
}

.strikeThroughText {
    text-decoration: line-through;
    color: #71737f;
}